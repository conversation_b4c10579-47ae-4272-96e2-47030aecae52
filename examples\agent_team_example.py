"""
多智能体团队协作示例
演示Planner、Writer、Examiner三个智能体的协作流程
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from autopatent.agents.planner_agent import PlannerAgent, PlanningStrategy
from autopatent.agents.writer_agent import WriterAgent, WriterType
from autopatent.agents.examiner_agent import ExaminerAgent
from autopatent.database.patent_db import PatentDB


def create_agent_team():
    """创建智能体团队"""
    print("🤖 创建智能体团队...")
    
    # 创建数据库
    db_path = "team_example.db"
    
    with PatentDB(db_path) as patent_db:
        # 添加一些示例专利用于RRAG
        sample_patents = [
            {
                'patent_id': 'CN101111111A',
                'title': '一种智能设备控制系统',
                'abstract': '本发明提供一种智能设备控制系统，能够自动控制各种智能设备。',
                'technical_field': '智能控制',
                'quality_score': 7.5,
                'filing_date': '2023-01-01T00:00:00'
            }
        ]
        
        for patent in sample_patents:
            patent_db.add_patent(patent)
        
        # 创建智能体团队
        team = {
            'planner': PlannerAgent(planning_strategy=PlanningStrategy.STANDARD),
            'writer': WriterAgent(writer_type=WriterType.GENERAL),
            'examiner': ExaminerAgent(patent_db=patent_db, enable_rrag=True)
        }
        
        print("✅ 智能体团队创建完成")
        return team, db_path


def team_collaboration_workflow():
    """演示团队协作工作流"""
    print("=== 多智能体协作工作流演示 ===")
    
    # 创建团队
    team, db_path = create_agent_team()
    
    # 专利概念
    concept = """
    一种基于物联网的智能家居安全监控系统，该系统集成了多种传感器、
    摄像头、人工智能分析引擎和移动应用程序，能够实时监控家庭安全状况，
    自动识别异常情况并及时报警。
    
    系统的核心创新包括：
    1. 多传感器融合技术，提高检测准确性
    2. 边缘计算架构，减少延迟和带宽需求
    3. 智能学习算法，适应用户生活习惯
    4. 隐私保护机制，确保用户数据安全
    """
    
    options = {
        'technical_field': '智能家居',
        'patent_type': 'invention',
        'target_quality': 8.0
    }
    
    print(f"📝 专利概念: {concept[:100]}...")
    print(f"🎯 目标质量: {options['target_quality']}")
    
    # 第一步：规划阶段
    print(f"\n🎯 第一步：规划阶段")
    print("-" * 30)
    
    planner = team['planner']
    planning_result = planner.process({
        'concept': concept,
        'options': options,
        'workflow_id': 'team_workflow_001'
    })
    
    if not planning_result['success']:
        print(f"❌ 规划失败: {planning_result['error']}")
        return
    
    pgtree = planning_result['pgtree']
    print(f"✅ 规划完成，生成 {len(pgtree.nodes)} 个节点")
    
    # 第二步：写作阶段
    print(f"\n✍️  第二步：写作阶段")
    print("-" * 30)
    
    writer = team['writer']
    sections_to_write = ['abstract', 'summary', 'claims']
    
    for section in sections_to_write:
        if section in pgtree.nodes:
            print(f"  📝 写作 {section}...")
            
            writing_result = writer.process({
                'pgtree': pgtree,
                'section': section,
                'concept': concept,
                'technical_field': options['technical_field'],
                'workflow_id': 'team_workflow_001'
            })
            
            if writing_result['success']:
                # 更新PGTree
                pgtree = writing_result['updated_pgtree']
                quality = writing_result['quality_score']
                print(f"    ✅ {section} 写作完成，质量评分: {quality:.2f}")
            else:
                print(f"    ❌ {section} 写作失败: {writing_result['error']}")
    
    # 第三步：审查阶段
    print(f"\n🔍 第三步：审查阶段")
    print("-" * 30)
    
    examiner = team['examiner']
    examination_result = examiner.process({
        'pgtree': pgtree,
        'section': 'all',
        'examination_type': 'substantive',
        'workflow_id': 'team_workflow_001'
    })
    
    if examination_result['success']:
        overall_score = examination_result['overall_score']
        issues_count = len(examination_result['issues'])
        
        print(f"✅ 审查完成")
        print(f"📊 总体评分: {overall_score:.2f}/10")
        print(f"⚠️  发现问题: {issues_count} 个")
        
        # 显示主要问题
        if examination_result['issues']:
            print(f"\n📋 主要问题:")
            for i, issue in enumerate(examination_result['issues'][:3], 1):
                print(f"  {i}. [{issue['issue_type']}] {issue['description']}")
        
        # RRAG结果
        if examination_result.get('rrag_result'):
            rrag = examination_result['rrag_result']
            print(f"\n🔬 RRAG分析:")
            print(f"  新颖性评分: {rrag['novelty_analysis']['novelty_score']:.2f}")
            print(f"  相似专利数: {len(rrag['retrieved_patents'])}")
        
        # 第四步：迭代改进（如果需要）
        if overall_score < options['target_quality'] and examination_result['issues']:
            print(f"\n🔄 第四步：迭代改进")
            print("-" * 30)
            
            # 选择质量最低的section进行改进
            issues_by_section = {}
            for issue in examination_result['issues']:
                section = issue.get('section', 'unknown')
                if section not in issues_by_section:
                    issues_by_section[section] = []
                issues_by_section[section].append(issue)
            
            # 改进质量最差的section
            for section, section_issues in list(issues_by_section.items())[:2]:
                if section in pgtree.nodes and section != 'unknown':
                    print(f"  🔧 改进 {section}...")
                    
                    # 生成改进反馈
                    feedback = [issue['suggestion'] for issue in section_issues if issue.get('suggestion')]
                    
                    if feedback:
                        current_content = pgtree.nodes[section].content
                        revised_content = writer.revise_content(
                            current_content=current_content,
                            feedback=feedback,
                            section=section
                        )
                        
                        if revised_content and revised_content != current_content:
                            # 更新PGTree
                            pgtree.nodes[section].content = revised_content
                            print(f"    ✅ {section} 改进完成")
                        else:
                            print(f"    ⚠️  {section} 改进效果有限")
            
            # 重新审查
            print(f"  🔍 重新审查...")
            final_examination = examiner.process({
                'pgtree': pgtree,
                'section': 'all',
                'examination_type': 'substantive',
                'workflow_id': 'team_workflow_001'
            })
            
            if final_examination['success']:
                final_score = final_examination['overall_score']
                improvement = final_score - overall_score
                print(f"    📊 最终评分: {final_score:.2f}/10 (改进: {improvement:+.2f})")
    else:
        print(f"❌ 审查失败: {examination_result['error']}")
    
    # 工作流总结
    print(f"\n" + "=" * 50)
    print(f"📊 工作流总结")
    print(f"=" * 50)
    
    # 统计信息
    planner_stats = planner.get_planning_statistics()
    writer_stats = writer.get_writing_statistics()
    examiner_stats = examiner.get_examination_statistics()
    
    print(f"🎯 规划统计:")
    print(f"  规划次数: {planner_stats.get('total_plannings', 0)}")
    print(f"  平均时间: {planner_stats.get('average_planning_time', 0):.2f}秒")
    
    print(f"\n✍️  写作统计:")
    print(f"  写作次数: {writer_stats.get('total_writings', 0)}")
    print(f"  修订次数: {writer_stats.get('revisions_made', 0)}")
    print(f"  平均质量: {writer_stats.get('average_quality_score', 0):.2f}")
    
    print(f"\n🔍 审查统计:")
    print(f"  审查次数: {examiner_stats.get('total_examinations', 0)}")
    print(f"  平均评分: {examiner_stats.get('average_quality_score', 0):.2f}")
    
    # Token使用统计
    total_tokens = 0
    for agent_name, agent in team.items():
        if hasattr(agent, 'get_token_usage'):
            usage = agent.get_token_usage()
            agent_tokens = usage.get('total_tokens', 0)
            total_tokens += agent_tokens
            print(f"\n🤖 {agent_name} Token使用:")
            print(f"  总Token: {agent_tokens:,}")
            print(f"  估计成本: ${usage.get('total_cost', 0):.4f}")
    
    print(f"\n💰 总Token使用: {total_tokens:,}")
    
    return pgtree


def main():
    """主函数"""
    print("🚀 多智能体团队协作演示")
    print("=" * 50)
    
    try:
        result_pgtree = team_collaboration_workflow()
        
        if result_pgtree:
            print(f"\n✅ 协作流程完成")
            print(f"📄 最终生成了 {len(result_pgtree.nodes)} 个专利section")
        else:
            print(f"\n❌ 协作流程未完成")
            
    except Exception as e:
        print(f"\n❌ 协作过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
