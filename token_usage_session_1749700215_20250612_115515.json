{
  "导出信息": {
    "导出时间": "2025-06-12T11:55:15.122556",
    "session_id": "session_1749700215",
    "记录数量": 2,
    "格式版本": "1.0"
  },
  "session统计": {
    "session_id": "session_1749700215",
    "start_time": "2025-06-12 11:50:15.111816",
    "total_input_tokens": 228,
    "total_output_tokens": 200,
    "total_reasoning_tokens": 0,
    "total_cost": 8.8e-05,
    "api_calls": 1,
    "successful_calls": 1,
    "failed_calls": 0,
    "operations": {
      "content_generation": 1
    },
    "models_used": {
      "deepseek-chat": 1
    },
    "agents_used": {
      "LLMClient": 1
    },
    "duration_seconds": 300.01074,
    "duration_formatted": "0:05:00",
    "total_tokens": 428,
    "average_response_time": 14.105750322341919,
    "success_rate": 100.0
  },
  "详细分析": {
    "总记录数": 2,
    "时间范围": {
      "开始时间": "2025-06-12T11:50:30.607349",
      "结束时间": "2025-06-12T11:50:30.607349"
    },
    "token统计": {
      "input": 228,
      "output": 200
    },
    "成本统计": {
      "input": 3.2e-05,
      "output": 5.6e-05
    },
    "总成本": 8.8e-05,
    "操作统计": {
      "content_generation": {
        "次数": 2,
        "tokens": 428,
        "成本": 8.8e-05
      }
    },
    "智能体统计": {
      "LLMClient": {
        "次数": 2,
        "tokens": 428,
        "成本": 8.8e-05
      }
    },
    "模型统计": {
      "deepseek-chat": {
        "次数": 2,
        "tokens": 428,
        "成本": 8.8e-05
      }
    },
    "性能统计": {
      "成功率": 100.0,
      "平均响应时间": 14.105750322341919,
      "最快响应": 14.105750322341919,
      "最慢响应": 14.105750322341919,
      "失败次数": 0
    },
    "时间趋势": {
      "2025-06-12 11:00": {
        "tokens": 428,
        "成本": 8.8e-05,
        "调用次数": 2
      }
    }
  },
  "成本估算": {
    "input_cost": 3.2e-05,
    "output_cost": 5.6e-05,
    "reasoning_cost": 0.0,
    "total_cost": 8.8e-05,
    "input_rate": 0.00014,
    "output_rate": 0.00028,
    "currency": "USD"
  },
  "原始记录": [
    {
      "timestamp": "2025-06-12T11:50:30.607349",
      "operation": "content_generation",
      "agent_name": "LLMClient",
      "token_type": "input",
      "count": 228,
      "model_name": "deepseek-chat",
      "cost_estimate": 3.2e-05,
      "response_time": 14.105750322341919,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:50:30.607349",
      "operation": "content_generation",
      "agent_name": "LLMClient",
      "token_type": "output",
      "count": 200,
      "model_name": "deepseek-chat",
      "cost_estimate": 5.6e-05,
      "response_time": 14.105750322341919,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    }
  ]
}_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:50:49.376057",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 1056,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000148,
      "response_time": 18.767713,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:50:49.376057",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 498,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000139,
      "response_time": 18.767713,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:50:49.376057",
      "operation": "writing",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 216,
      "model_name": "deepseek-chat",
      "cost_estimate": 3e-05,
      "response_time": 34.26524,
      "session_id": "session_1749700215",
      "workflow_id": "smart_mining_metaverse_fixed_1749700192",
      "node_id": "abstract",
      "input_length": 270,
      "output_length": 533,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:50:49.376057",
      "operation": "writing",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 426,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000119,
      "response_time": 34.26524,
      "session_id": "session_1749700215",
      "workflow_id": "smart_mining_metaverse_fixed_1749700192",
      "node_id": "abstract",
      "input_length": 270,
      "output_length": 533,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:51:12.269305",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 365,
      "model_name": "deepseek-chat",
      "cost_estimate": 5.1e-05,
      "response_time": 22.891227,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:51:12.269305",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 579,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000162,
      "response_time": 22.891227,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:51:54.059311",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 973,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000136,
      "response_time": 41.790006,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:51:54.059311",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 1301,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000364,
      "response_time": 41.790006,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:51:54.059311",
      "operation": "writing",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 658,
      "model_name": "deepseek-chat",
      "cost_estimate": 9.2e-05,
      "response_time": 64.681233,
      "session_id": "session_1749700215",
      "workflow_id": "smart_mining_metaverse_fixed_1749700192",
      "node_id": "background",
      "input_length": 823,
      "output_length": 1398,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:51:54.059311",
      "operation": "writing",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 1118,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000313,
      "response_time": 64.681233,
      "session_id": "session_1749700215",
      "workflow_id": "smart_mining_metaverse_fixed_1749700192",
      "node_id": "background",
      "input_length": 823,
      "output_length": 1398,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:52:28.859811",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 351,
      "model_name": "deepseek-chat",
      "cost_estimate": 4.9e-05,
      "response_time": 34.798617,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:52:28.859811",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 976,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000273,
      "response_time": 34.798617,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:53:24.688124",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 1297,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000182,
      "response_time": 55.828313,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:53:24.688124",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 1766,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000494,
      "response_time": 55.828313,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:53:24.688124",
      "operation": "writing",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 1107,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000155,
      "response_time": 90.627934,
      "session_id": "session_1749700215",
      "workflow_id": "smart_mining_metaverse_fixed_1749700192",
      "node_id": "summary",
      "input_length": 1384,
      "output_length": 2012,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:53:24.688124",
      "operation": "writing",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 1609,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000451,
      "response_time": 90.627934,
      "session_id": "session_1749700215",
      "workflow_id": "smart_mining_metaverse_fixed_1749700192",
      "node_id": "summary",
      "input_length": 1384,
      "output_length": 2012,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:53:56.530276",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 323,
      "model_name": "deepseek-chat",
      "cost_estimate": 4.5e-05,
      "response_time": 31.84118,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:53:56.530276",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 906,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000254,
      "response_time": 31.84118,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:54:39.335804",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 1259,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000176,
      "response_time": 42.804518,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:54:39.335804",
      "operation": "content_generation",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 1153,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000323,
      "response_time": 42.804518,
      "session_id": "session_1749700215",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:54:39.335804",
      "operation": "writing",
      "agent_name": "WriterAgent_general",
      "token_type": "input",
      "count": 1571,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.00022,
      "response_time": 74.646708,
      "session_id": "session_1749700215",
      "workflow_id": "smart_mining_metaverse_fixed_1749700192",
      "node_id": "detailed_description",
      "input_length": 1964,
      "output_length": 1386,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:54:39.335804",
      "operation": "writing",
      "agent_name": "WriterAgent_general",
      "token_type": "output",
      "count": 1108,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.00031,
      "response_time": 74.646708,
      "session_id": "session_1749700215",
      "workflow_id": "smart_mining_metaverse_fixed_1749700192",
      "node_id": "detailed_description",
      "input_length": 1964,
      "output_length": 1386,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    }
  ]
}