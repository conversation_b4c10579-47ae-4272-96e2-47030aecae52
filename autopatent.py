#!/usr/bin/env python3
"""
AutoPatent主启动器 - 统一入口
"""

import sys
import argparse
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AutoPatent - 多智能体专利生成系统')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 运行命令
    run_parser = subparsers.add_parser('run', help='运行专利生成')
    run_parser.add_argument('--demo', action='store_true', help='演示模式')
    run_parser.add_argument('--interactive', '-i', action='store_true', help='交互模式')
    run_parser.add_argument('--concept', '-c', type=str, help='专利概念')
    run_parser.add_argument('--field', '-f', type=str, default='通用技术', help='技术领域')
    run_parser.add_argument('--output', '-o', type=str, help='输出文件')
    
    # 测试命令
    test_parser = subparsers.add_parser('test', help='运行测试')
    test_parser.add_argument('--module', '-m', type=str, help='测试特定模块')
    
    # 可视化命令
    viz_parser = subparsers.add_parser('visualize', help='生成可视化报告')
    viz_parser.add_argument('--input', '-i', type=str, help='输入数据文件')
    viz_parser.add_argument('--output', '-o', type=str, default='reports', help='输出目录')
    
    # 工具命令
    tools_parser = subparsers.add_parser('tools', help='工具命令')
    tools_parser.add_argument('--organize', action='store_true', help='整理项目结构')
    tools_parser.add_argument('--install-deps', action='store_true', help='安装依赖')
    
    args = parser.parse_args()
    
    if args.command == 'run':
        from scripts.run_autopatent_uv import main as run_main
        # 重新构造参数
        sys.argv = ['run_autopatent_uv.py']
        if args.demo:
            sys.argv.append('--demo')
        elif args.interactive:
            sys.argv.append('--interactive')
        elif args.concept:
            sys.argv.extend(['-c', args.concept, '-f', args.field])
            if args.output:
                sys.argv.extend(['-o', args.output])
        run_main()
        
    elif args.command == 'test':
        from scripts.test_project import main as test_main
        test_main()
        
    elif args.command == 'visualize':
        print("🎨 启动可视化功能...")
        try:
            from autopatent.utils.enhanced_token_visualizer import EnhancedTokenVisualizer
            
            if args.input:
                visualizer = EnhancedTokenVisualizer.from_export_file(args.input)
            else:
                visualizer = EnhancedTokenVisualizer()
                
            report_path = visualizer.create_enhanced_dashboard(args.output)
            print(f"✅ 可视化报告已生成: {report_path}")
        except Exception as e:
            print(f"❌ 生成可视化报告失败: {e}")
            
    elif args.command == 'tools':
        if args.organize:
            from scripts.organize_project import organize_project_structure
            organize_project_structure()
        elif args.install_deps:
            from scripts.install_dependencies import main as install_main
            install_main()
        else:
            tools_parser.print_help()
            
    else:
        parser.print_help()
        print("\n💡 常用命令:")
        print("  python autopatent.py run --demo                    # 运行演示")
        print("  python autopatent.py run --interactive             # 交互模式")
        print("  python autopatent.py run -c '您的概念' -f '技术领域'  # 直接生成")
        print("  python autopatent.py test                          # 运行测试")
        print("  python autopatent.py visualize -i data.json        # 生成可视化")
        print("  python autopatent.py tools --organize              # 整理项目")

if __name__ == "__main__":
    main()
