#!/usr/bin/env python3
"""
测试新的项目结构
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_imports():
    """测试导入是否正常"""
    print("🧪 测试模块导入...")

    try:
        # 测试基础模块导入
        from autopatent.agents.base_agent import BaseAgent

        print("✅ BaseAgent导入成功")

        from autopatent.agents.planner_agent import PlannerAgent

        print("✅ PlannerAgent导入成功")

        from autopatent.agents.writer_agent import WriterAgent

        print("✅ WriterAgent导入成功")

        from autopatent.agents.examiner_agent import ExaminerAgent

        print("✅ ExaminerAgent导入成功")

        from autopatent.utils.llm_client import LLMClient

        print("✅ LLMClient导入成功")

        from autopatent.utils.pg_tree_handler import PGTreeHandler

        print("✅ PGTreeHandler导入成功")

        from autopatent.database.patent_db import PatentDB

        print("✅ PatentDB导入成功")

        from config.config import AutoPatentConfig

        print("✅ Config导入成功")

        return True

    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")

    try:
        # 测试配置
        from config.config import AutoPatentConfig

        config = AutoPatentConfig()
        print(f"✅ 配置加载成功")

        # 测试LLM客户端（模拟模式）
        from autopatent.utils.llm_client import LLMClient, ModelConfig, ModelProvider

        mock_config = ModelConfig(
            provider=ModelProvider.MOCK,
            model_name="test-model",
            api_key="mock_key",
            base_url="mock_url",
        )

        llm_client = LLMClient(mock_config)
        print("✅ LLM客户端创建成功")

        # 测试智能体创建
        from autopatent.agents.planner_agent import PlannerAgent, PlanningStrategy

        planner = PlannerAgent(planning_strategy=PlanningStrategy.STANDARD)
        print("✅ PlannerAgent创建成功")

        from autopatent.agents.writer_agent import WriterAgent, WriterType, WritingStyle

        writer = WriterAgent(
            writer_type=WriterType.GENERAL, writing_style=WritingStyle.FORMAL
        )
        print("✅ WriterAgent创建成功")

        return True

    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_file_structure():
    """测试文件结构"""
    print("\n📁 检查文件结构...")

    required_files = [
        "autopatent/__init__.py",
        "autopatent/agents/__init__.py",
        "autopatent/agents/base_agent.py",
        "autopatent/agents/planner_agent.py",
        "autopatent/agents/writer_agent.py",
        "autopatent/agents/examiner_agent.py",
        "autopatent/utils/__init__.py",
        "autopatent/utils/llm_client.py",
        "autopatent/utils/pg_tree_handler.py",
        "autopatent/database/__init__.py",
        "autopatent/database/patent_db.py",
        "autopatent/cli/__init__.py",
        "autopatent/cli/cli.py",
        "config/config.py",
        "main.py",
        "run.py",
    ]

    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")

    if missing_files:
        print(f"\n❌ 缺少文件:")
        for file in missing_files:
            print(f"  - {file}")
        return False

    print("\n✅ 所有必需文件都存在")
    return True


def test_examples():
    """测试示例文件"""
    print("\n📚 检查示例文件...")

    example_dirs = ["examples/patent_generation", "output/patents"]

    for dir_path in example_dirs:
        if Path(dir_path).exists():
            files = list(Path(dir_path).glob("*"))
            print(f"✅ {dir_path}: {len(files)} 个文件")
        else:
            print(f"❌ {dir_path}: 目录不存在")


def main():
    """主测试函数"""
    print("🏔️  AutoPatent 项目结构测试")
    print("=" * 50)

    # 测试文件结构
    structure_ok = test_file_structure()

    # 测试导入
    import_ok = test_imports()

    # 测试基本功能
    function_ok = test_basic_functionality()

    # 测试示例
    test_examples()

    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"  文件结构: {'✅ 通过' if structure_ok else '❌ 失败'}")
    print(f"  模块导入: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"  基本功能: {'✅ 通过' if function_ok else '❌ 失败'}")

    if structure_ok and import_ok and function_ok:
        print("\n🎉 所有测试通过！项目结构重组成功！")
        print("\n💡 现在可以使用以下命令运行系统:")
        print("  python main.py --demo          # 运行演示")
        print("  python main.py --interactive   # 交互模式")
        print("  python run.py                  # 快速运行")
        return True
    else:
        print("\n❌ 部分测试失败，需要修复")
        return False


if __name__ == "__main__":
    main()
