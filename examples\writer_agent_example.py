"""
WriterAgent 完整使用示例
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from autopatent.agents.writer_agent import WriterAgent, WriterType, WritingStyle
from autopatent.utils.pg_tree_handler import PGTreeHandler, PGTreeNode, NodeStatus


def create_sample_pgtree():
    """创建示例PGTree用于写作测试"""
    pgtree = PGTreeHandler()
    
    # 创建一个规划好的PGTree结构
    sections_data = {
        'abstract': {
            'section_name': 'abstract',
            'content': '',  # 待写作
            'status': NodeStatus.PENDING,
            'priority': 'high',
            'requirements': '简洁明了，突出技术特点和有益效果'
        },
        'background': {
            'section_name': 'background',
            'content': '',  # 待写作
            'status': NodeStatus.PENDING,
            'priority': 'medium',
            'requirements': '详细描述现有技术问题和本发明的必要性'
        },
        'summary': {
            'section_name': 'summary',
            'content': '',  # 待写作
            'status': NodeStatus.PENDING,
            'priority': 'high',
            'requirements': '清晰描述技术方案和有益效果'
        },
        'claims': {
            'section_name': 'claims',
            'content': '',  # 待写作
            'status': NodeStatus.PENDING,
            'priority': 'critical',
            'requirements': '权利要求书，逻辑清晰，保护范围合适'
        }
    }
    
    # 添加节点到PGTree
    for node_id, data in sections_data.items():
        node = PGTreeNode(
            node_id=node_id,
            section_name=data['section_name'],
            content=data['content'],
            status=data['status'],
            priority=data['priority']
        )
        # 添加写作要求
        if hasattr(node, 'metadata'):
            node.metadata['requirements'] = data['requirements']
        pgtree.add_node(node)
    
    return pgtree


def test_basic_writing():
    """测试基本写作功能"""
    print("=== WriterAgent 基本写作测试 ===")
    
    # 创建写作智能体
    writer = WriterAgent(
        writer_type=WriterType.GENERAL,
        writing_style=WritingStyle.FORMAL
    )
    
    # 准备写作上下文
    concept = """
    一种基于人工智能的智能客服系统，该系统集成了自然语言处理、
    语音识别、情感分析和知识图谱技术，能够提供24小时智能客服服务。
    
    系统的核心创新包括：
    1. 多模态交互界面，支持文字、语音、图像输入
    2. 上下文理解引擎，能够理解复杂的用户意图
    3. 情感识别模块，根据用户情绪调整回复策略
    4. 自学习机制，持续优化服务质量
    """
    
    # 创建PGTree
    pgtree = create_sample_pgtree()
    
    print(f"📝 专利概念: {concept[:100]}...")
    print(f"✍️  写作类型: {writer.writer_type.value}")
    print(f"🎨 写作风格: {writer.writing_style.value}")
    
    # 测试写作abstract
    print(f"\n🚀 开始写作 Abstract...")
    
    writing_input = {
        'pgtree': pgtree,
        'section': 'abstract',
        'concept': concept,
        'technical_field': '人工智能',
        'workflow_id': 'writer_example_001'
    }
    
    result = writer.process(writing_input)
    
    if result['success']:
        print("✅ Abstract 写作成功")
        
        generated_content = result['generated_content']
        quality_score = result['quality_score']
        writing_metadata = result['writing_metadata']
        
        print(f"📊 质量评分: {quality_score:.2f}/10")
        print(f"⏱️  写作时间: {writing_metadata['processing_time']:.2f}秒")
        print(f"📏 内容长度: {len(generated_content)} 字符")
        
        print(f"\n📄 生成的Abstract:")
        print("-" * 50)
        print(generated_content[:300] + "..." if len(generated_content) > 300 else generated_content)
        print("-" * 50)
        
        # 显示写作建议
        if result.get('writing_suggestions'):
            print(f"\n💭 写作建议:")
            for suggestion in result['writing_suggestions'][:3]:
                print(f"  • {suggestion}")
    else:
        print(f"❌ Abstract 写作失败: {result['error']}")
    
    return result


def test_different_writer_types():
    """测试不同类型的写作智能体"""
    print("\n=== 不同写作类型测试 ===")
    
    concept = "一种新型的太阳能电池板，具有高效率和低成本的特点。"
    pgtree = create_sample_pgtree()
    
    writer_types = [
        WriterType.GENERAL,
        WriterType.TECHNICAL,
        WriterType.LEGAL
    ]
    
    for writer_type in writer_types:
        print(f"\n🎯 测试写作类型: {writer_type.value}")
        
        writer = WriterAgent(writer_type=writer_type)
        
        result = writer.process({
            'pgtree': pgtree,
            'section': 'summary',
            'concept': concept,
            'technical_field': '新能源技术'
        })
        
        if result['success']:
            content = result['generated_content']
            quality = result['quality_score']
            
            print(f"  📊 质量评分: {quality:.2f}/10")
            print(f"  📏 内容长度: {len(content)} 字符")
            print(f"  📄 内容预览: {content[:100]}...")
        else:
            print(f"  ❌ 失败: {result['error']}")


def test_writing_styles():
    """测试不同写作风格"""
    print("\n=== 不同写作风格测试 ===")
    
    concept = "一种智能手表的健康监测功能，能够实时监测心率、血压等生理指标。"
    pgtree = create_sample_pgtree()
    
    styles = [
        WritingStyle.FORMAL,
        WritingStyle.TECHNICAL,
        WritingStyle.CONCISE
    ]
    
    writer = WriterAgent(writer_type=WriterType.GENERAL)
    
    for style in styles:
        print(f"\n🎨 测试写作风格: {style.value}")
        
        writer.writing_style = style
        
        result = writer.process({
            'pgtree': pgtree,
            'section': 'background',
            'concept': concept,
            'technical_field': '可穿戴设备'
        })
        
        if result['success']:
            content = result['generated_content']
            metadata = result['writing_metadata']
            
            print(f"  📊 质量评分: {result['quality_score']:.2f}/10")
            print(f"  ⏱️  写作时间: {metadata['processing_time']:.2f}秒")
            print(f"  📄 风格特点: {metadata.get('style_characteristics', 'N/A')}")
            print(f"  📝 内容预览: {content[:120]}...")
        else:
            print(f"  ❌ 失败: {result['error']}")


def test_content_revision():
    """测试内容修订功能"""
    print("\n=== 内容修订测试 ===")
    
    writer = WriterAgent(writer_type=WriterType.TECHNICAL)
    
    # 原始内容（模拟已写作的内容）
    original_content = """
    本发明涉及一种智能温控系统。该系统可以自动调节温度。
    系统包括传感器和控制器。传感器检测温度，控制器调节设备。
    """
    
    # 修订反馈
    feedback = [
        "内容过于简单，需要增加技术细节",
        "缺少具体的技术方案描述",
        "应该说明系统的创新点和优势",
        "需要使用更专业的技术术语"
    ]
    
    print(f"📄 原始内容: {original_content}")
    print(f"📝 修订反馈: {len(feedback)} 条")
    
    # 执行修订
    revised_content = writer.revise_content(
        current_content=original_content,
        feedback=feedback,
        section='summary'
    )
    
    if revised_content and revised_content != original_content:
        print(f"\n✅ 修订成功")
        print(f"📄 修订后内容:")
        print("-" * 50)
        print(revised_content)
        print("-" * 50)
        
        # 比较修订前后
        print(f"\n📊 修订对比:")
        print(f"  原始长度: {len(original_content)} 字符")
        print(f"  修订长度: {len(revised_content)} 字符")
        print(f"  长度变化: {len(revised_content) - len(original_content):+d} 字符")
    else:
        print(f"❌ 修订失败或无变化")


def test_quality_optimization():
    """测试质量优化功能"""
    print("\n=== 质量优化测试 ===")
    
    writer = WriterAgent(
        writer_type=WriterType.GENERAL,
        enable_quality_optimization=True
    )
    
    concept = """
    一种基于区块链的供应链追溯系统，能够记录产品从生产到销售的全过程，
    确保产品质量和来源的可追溯性。系统具有防篡改、透明度高的特点。
    """
    
    pgtree = create_sample_pgtree()
    
    print(f"📝 概念: {concept[:100]}...")
    print(f"🎯 启用质量优化")
    
    result = writer.process({
        'pgtree': pgtree,
        'section': 'claims',
        'concept': concept,
        'technical_field': '区块链技术',
        'quality_requirements': {
            'min_score': 8.0,
            'enable_optimization': True,
            'max_optimization_rounds': 3
        }
    })
    
    if result['success']:
        metadata = result['writing_metadata']
        
        print(f"✅ 质量优化完成")
        print(f"📊 最终质量评分: {result['quality_score']:.2f}/10")
        print(f"🔄 优化轮次: {metadata.get('optimization_rounds', 0)}")
        print(f"⏱️  总处理时间: {metadata['processing_time']:.2f}秒")
        
        if metadata.get('optimization_history'):
            print(f"\n📈 优化历史:")
            for i, step in enumerate(metadata['optimization_history']):
                print(f"  轮次 {i+1}: 评分 {step.get('score', 0):.2f}")
        
        print(f"\n📄 优化后内容预览:")
        content = result['generated_content']
        print(content[:200] + "..." if len(content) > 200 else content)
    else:
        print(f"❌ 质量优化失败: {result['error']}")


def main():
    """主函数 - 运行所有测试"""
    print("🚀 WriterAgent 完整测试套件")
    print("=" * 50)
    
    try:
        # 基本写作测试
        basic_result = test_basic_writing()
        
        # 不同写作类型测试
        test_different_writer_types()
        
        # 不同写作风格测试
        test_writing_styles()
        
        # 内容修订测试
        test_content_revision()
        
        # 质量优化测试
        test_quality_optimization()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成")
        
        # 显示统计信息
        if basic_result and basic_result['success']:
            writer = WriterAgent()
            stats = writer.get_writing_statistics()
            print(f"\n📊 写作统计:")
            print(f"  总写作次数: {stats.get('total_writings', 0)}")
            print(f"  平均写作时间: {stats.get('average_writing_time', 0):.2f}秒")
            print(f"  平均质量评分: {stats.get('average_quality_score', 0):.2f}")
            print(f"  修订次数: {stats.get('revisions_made', 0)}")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
