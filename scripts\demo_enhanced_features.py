#!/usr/bin/env python3
"""
AutoPatent增强功能演示 - Token追踪和可视化
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def demo_token_tracking():
    """演示Token追踪功能"""
    print("🔍 演示Token追踪功能...")
    
    try:
        # 模拟Token追踪
        from utils.token_tracker import TokenTracker, OperationType
        
        with TokenTracker(session_id="demo_tracking") as tracker:
            # 模拟规划阶段
            tracker.add_tokens(
                operation=OperationType.PLANNING,
                agent_name="PlannerAgent",
                input_tokens=1500,
                output_tokens=800,
                model_name="deepseek-chat",
                response_time=2.5,
                success=True
            )
            
            # 模拟写作阶段
            tracker.add_tokens(
                operation=OperationType.WRITING,
                agent_name="WriterAgent",
                input_tokens=2000,
                output_tokens=1200,
                model_name="deepseek-chat",
                response_time=3.2,
                success=True
            )
            
            # 模拟审查阶段
            tracker.add_tokens(
                operation=OperationType.EXAMINING,
                agent_name="ExaminerAgent",
                input_tokens=1000,
                output_tokens=500,
                model_name="deepseek-chat",
                response_time=1.8,
                success=True
            )
            
            # 获取统计信息
            usage = tracker.get_usage()
            print(f"✅ 总Token数: {usage['total_tokens']:,}")
            print(f"💰 总成本: ${usage['total_cost']:.4f}")
            print(f"📞 API调用: {usage['api_calls']} 次")
            print(f"✅ 成功率: {usage['success_rate']:.1f}%")
            
            # 导出数据
            export_path = tracker.export_usage(
                filepath="output/demo_token_usage",
                format_type="json"
            )
            print(f"📁 数据已导出: {export_path}")
            
            return export_path
            
    except ImportError as e:
        print(f"❌ 导入Token追踪模块失败: {e}")
        return None

def demo_visualization(data_file=None):
    """演示可视化功能"""
    print("\n🎨 演示可视化功能...")
    
    try:
        # 尝试导入增强版可视化器
        try:
            from autopatent.utils.enhanced_token_visualizer import EnhancedTokenVisualizer
        except ImportError:
            from utils.token_visualizer import TokenUsageVisualizer as EnhancedTokenVisualizer
        
        # 创建可视化器
        if data_file and os.path.exists(data_file):
            visualizer = EnhancedTokenVisualizer.from_export_file(data_file)
            print(f"✅ 从文件加载数据: {data_file}")
        else:
            # 创建模拟数据
            visualizer = EnhancedTokenVisualizer()
            visualizer._session_data = {
                "session统计": {
                    "session_id": "demo_visualization",
                    "total_tokens": 5000,
                    "total_input_tokens": 2000,
                    "total_output_tokens": 2500,
                    "total_reasoning_tokens": 500,
                    "total_cost": 0.05,  # USD
                    "api_calls": 15,
                    "success_rate": 95.0,
                    "operations": {"planning": 5, "writing": 8, "examining": 2},
                    "models_used": {"deepseek-chat": 12, "deepseek-reasoner": 3},
                    "agents_used": {"PlannerAgent": 5, "WriterAgent": 8, "ExaminerAgent": 2}
                }
            }
            print("✅ 使用模拟数据")
        
        # 生成报告
        if hasattr(visualizer, 'create_enhanced_dashboard'):
            report_path = visualizer.create_enhanced_dashboard("output/demo_reports")
        else:
            report_path = visualizer.create_usage_dashboard("output/demo_reports")
        
        print(f"📊 可视化报告已生成: {report_path}")
        return report_path
        
    except ImportError as e:
        print(f"❌ 导入可视化模块失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 生成可视化报告失败: {e}")
        return None

def demo_integrated_manager():
    """演示集成Token管理器"""
    print("\n🔧 演示集成Token管理器...")
    
    try:
        from autopatent.utils.integrated_token_manager import create_token_manager
        
        with create_token_manager(session_id="demo_integrated") as manager:
            # 模拟智能体操作
            manager.track_agent_operation(
                agent_name="PlannerAgent",
                operation="planning",
                input_tokens=1500,
                output_tokens=800,
                model_name="deepseek-chat",
                response_time=2.5,
                success=True
            )
            
            manager.track_agent_operation(
                agent_name="WriterAgent",
                operation="writing",
                input_tokens=2000,
                output_tokens=1200,
                model_name="deepseek-chat",
                response_time=3.2,
                success=True
            )
            
            # 获取成本分解
            cost_breakdown = manager.get_cost_breakdown()
            if "error" not in cost_breakdown:
                print("💰 成本分解:")
                for operation, details in cost_breakdown.get("by_operation", {}).items():
                    print(f"   {operation}: ￥{details['cost_cny']:.4f}")
            
            # 导出数据
            export_path = manager.export_data(format_type="json")
            if export_path:
                print(f"📁 数据已导出: {export_path}")
            
            # 生成报告
            report_path = manager.generate_report()
            if report_path:
                print(f"📊 报告已生成: {report_path}")
        
        print("✅ 集成Token管理器演示完成")
        
    except ImportError as e:
        print(f"❌ 导入集成管理器失败: {e}")
    except Exception as e:
        print(f"❌ 集成管理器演示失败: {e}")

def demo_currency_features():
    """演示人民币计费功能"""
    print("\n💰 演示人民币计费功能...")
    
    # 模拟成本计算
    usd_costs = [0.001, 0.005, 0.01, 0.05, 0.1]
    usd_to_cny_rate = 7.2
    
    print("USD -> CNY 成本转换:")
    print("-" * 30)
    for usd_cost in usd_costs:
        cny_cost = usd_cost * usd_to_cny_rate
        print(f"${usd_cost:.4f} -> ￥{cny_cost:.4f}")
    
    print("\n✅ 支持双币种显示:")
    print("• 原始成本以美元计算")
    print("• 自动转换为人民币显示")
    print("• DeepSeek API实际以人民币计费")

def demo_bilingual_ui():
    """演示中英混合UI"""
    print("\n🌐 演示中英混合UI功能...")
    
    # 模拟UI标签
    labels = {
        'title': 'Token使用概览 (Token Usage Overview)',
        'metrics': {
            'total_tokens': 'Total Tokens',
            'total_cost_cny': '总成本 (￥)',
            'api_calls': 'API Calls',
            'success_rate': 'Success Rate'
        },
        'charts': {
            'token_distribution': 'Token类型分布 (Token Type Distribution)',
            'operation_distribution': '操作类型分布 (Operation Distribution)',
            'cost_trend': '成本趋势 (Cost Trend)'
        }
    }
    
    print("🎨 UI标签示例:")
    print("-" * 40)
    for category, items in labels.items():
        if isinstance(items, dict):
            print(f"{category.upper()}:")
            for key, value in items.items():
                print(f"  • {value}")
        else:
            print(f"• {items}")
    
    print("\n✅ 特色:")
    print("• 专业术语保持英文原文")
    print("• 常用界面元素中英对照")
    print("• 变量名和技术名词使用英文")

def main():
    """主演示函数"""
    print("🚀 AutoPatent增强功能演示")
    print("=" * 60)
    
    # 确保输出目录存在
    os.makedirs("output", exist_ok=True)
    os.makedirs("output/demo_reports", exist_ok=True)
    
    # 1. 演示Token追踪
    export_file = demo_token_tracking()
    
    # 2. 演示可视化
    demo_visualization(export_file)
    
    # 3. 演示集成管理器
    demo_integrated_manager()
    
    # 4. 演示人民币计费
    demo_currency_features()
    
    # 5. 演示中英混合UI
    demo_bilingual_ui()
    
    print("\n" + "=" * 60)
    print("🎉 所有增强功能演示完成！")
    print("\n💡 主要特色:")
    print("✅ 完善的Token追踪和成本分析")
    print("✅ 中英混合UI界面")
    print("✅ 人民币计费支持")
    print("✅ 专业术语保持英文")
    print("✅ 完美融入AutoPatent项目")
    print("\n📁 查看生成的报告:")
    print("• output/demo_reports/ - 可视化报告")
    print("• output/ - 导出的数据文件")

if __name__ == "__main__":
    main()
