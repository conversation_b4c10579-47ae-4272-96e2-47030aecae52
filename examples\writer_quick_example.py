"""
WriterAgent 快速使用示例
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from autopatent.agents.writer_agent import WriterAgent, WriterType
from autopatent.utils.pg_tree_handler import PGTreeHandler, PGTreeNode, NodeStatus


def quick_writing_example():
    """快速写作示例"""
    print("=== WriterAgent 快速示例 ===")

    # 创建写作智能体
    writer = WriterAgent(writer_type=WriterType.GENERAL)

    # 创建简单的PGTree
    pgtree = PGTreeHandler()

    # 添加一个待写作的节点
    node = PGTreeNode(
        node_id="abstract",
        section_name="abstract",
        content="",  # 待写作
        status=NodeStatus.PENDING,
        priority="high",
    )
    pgtree.add_node(node)

    # 专利概念
    concept = """
    一种智能垃圾分类系统，使用计算机视觉技术自动识别垃圾类型，
    并控制机械臂将垃圾投放到相应的分类箱中。
    """

    print(f"📝 专利概念: {concept[:80]}...")
    print(f"✍️  写作类型: {writer.writer_type.value}")
    print(f"📄 目标section: abstract")

    # 执行写作
    result = writer.process(
        {
            "pgtree": pgtree,
            "section": "abstract",
            "concept": concept,
            "technical_field": "人工智能",
        }
    )

    # 显示结果
    if result["success"]:
        content = result["content"]
        quality = result["quality_score"]
        metadata = result["writing_metadata"]

        print(f"\n✅ 写作成功")
        print(f"📊 质量评分: {quality:.2f}/10")
        print(f"📏 内容长度: {len(content)} 字符")
        print(f"⏱️  写作时间: {metadata['processing_time']:.2f}秒")

        print(f"\n📄 生成的Abstract:")
        print("-" * 40)
        print(content)
        print("-" * 40)
    else:
        print(f"❌ 写作失败: {result['error']}")


if __name__ == "__main__":
    quick_writing_example()
