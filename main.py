#!/usr/bin/env python3
"""
AutoPatent - 智能专利生成系统

主入口文件
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from autopatent.cli import main as cli_main


def main():
    """主函数"""
    print("🏔️  AutoPatent - 智能专利生成系统")
    print("=" * 50)

    try:
        cli_main()
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
