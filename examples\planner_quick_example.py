"""
PlannerAgent 快速使用示例
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from autopatent.agents.planner_agent import PlannerAgent, PlanningStrategy


def quick_planning_example():
    """快速规划示例"""
    print("=== PlannerAgent 快速示例 ===")

    # 创建规划智能体
    planner = PlannerAgent(planning_strategy=PlanningStrategy.STANDARD)

    # 简单的专利概念
    concept = """
    一种智能手机无线充电器，具有自动对齐和快速充电功能。
    当手机放置在充电器上时，系统能够自动检测手机位置并调整充电线圈位置，
    实现最佳充电效率。
    """

    # 基本选项
    options = {"technical_field": "电子设备", "patent_type": "invention"}

    print(f"📝 专利概念: {concept[:80]}...")
    print(f"🔬 技术领域: {options['technical_field']}")

    # 执行规划
    result = planner.process({"concept": concept, "options": options})

    # 显示结果
    if result["success"]:
        pgtree = result["pgtree"]
        metadata = result["planning_metadata"]

        print(f"\n✅ 规划成功")
        print(f"📊 策略: {pgtree.strategy.value}")
        print(f"🔢 节点数: {len(pgtree.structure)}")
        print(f"⏱️  耗时: {metadata.get('processing_time', 0):.2f}秒")
        print(f"📝 总预估字数: {pgtree.total_estimated_words}")
        print(f"⏰ 总预估时间: {pgtree.total_estimated_time}分钟")

        print(f"\n📋 生成的section:")
        for section_id, section_plan in pgtree.structure.items():
            priority_icon = (
                "🔴"
                if section_plan.priority == "critical"
                else "🟡" if section_plan.priority == "high" else "🟢"
            )
            print(f"  {priority_icon} {section_id}: {section_plan.section_name}")
            print(f"    📏 预估字数: {section_plan.estimated_words}")
            print(f"    ⏱️  预估时间: {section_plan.estimated_time_minutes}分钟")
    else:
        print(f"❌ 规划失败: {result['error']}")


if __name__ == "__main__":
    quick_planning_example()
