#!/usr/bin/env python3
"""
修复AutoPatent项目导入问题的脚本
"""

import os
import re
from pathlib import Path

def fix_relative_imports():
    """修复相对导入问题"""
    print("🔧 修复相对导入问题...")
    
    # 需要修复的文件和对应的导入修复
    fixes = [
        # agents/planner_agent.py
        {
            'file': 'agents/planner_agent.py',
            'fixes': [
                ('from ..utils.pg_tree_handler import', 'from utils.pg_tree_handler import'),
                ('from ..utils.token_tracker import', 'from utils.token_tracker import'),
            ]
        },
        # agents/writer_agent.py  
        {
            'file': 'agents/writer_agent.py',
            'fixes': [
                ('from ..utils.llm_client import', 'from utils.llm_client import'),
                ('from ..utils.token_tracker import', 'from utils.token_tracker import'),
            ]
        },
        # agents/examiner_agent.py
        {
            'file': 'agents/examiner_agent.py', 
            'fixes': [
                ('from ..database.patent_db import', 'from database.patent_db import'),
                ('from ..utils.llm_client import', 'from utils.llm_client import'),
            ]
        },
        # coordinator/coordinator.py
        {
            'file': 'coordinator/coordinator.py',
            'fixes': [
                ('from ..agents.planner_agent import', 'from agents.planner_agent import'),
                ('from ..agents.writer_agent import', 'from agents.writer_agent import'),
                ('from ..agents.examiner_agent import', 'from agents.examiner_agent import'),
                ('from ..database.patent_db import', 'from database.patent_db import'),
                ('from ..utils.token_tracker import', 'from utils.token_tracker import'),
            ]
        }
    ]
    
    for fix_info in fixes:
        file_path = fix_info['file']
        if os.path.exists(file_path):
            print(f"修复文件: {file_path}")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 应用修复
                for old_import, new_import in fix_info['fixes']:
                    if old_import in content:
                        content = content.replace(old_import, new_import)
                        print(f"  ✅ 修复: {old_import} -> {new_import}")
                
                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
            except Exception as e:
                print(f"  ❌ 修复失败: {e}")
        else:
            print(f"⚠️  文件不存在: {file_path}")

def create_missing_files():
    """创建缺失的关键文件"""
    print("\n📁 检查并创建缺失的文件...")
    
    # 检查是否存在.env.example文件
    if not os.path.exists('.env.example'):
        print("创建 .env.example 文件...")
        with open('.env.example', 'w', encoding='utf-8') as f:
            f.write("""# AutoPatent环境配置文件
# 复制此文件为.env并填入真实的API密钥

# DeepSeek API密钥（必需）
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 可选配置
DATABASE_PATH=patents.db
LOG_LEVEL=INFO
MAX_ITERATIONS=3
QUALITY_THRESHOLD=7.0

# LLM配置
LLM_MODEL=deepseek-chat
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=4000
""")
        print("✅ .env.example 文件已创建")
    
    # 检查是否存在__init__.py文件
    init_files = [
        'agents/__init__.py',
        'coordinator/__init__.py', 
        'database/__init__.py',
        'utils/__init__.py'
    ]
    
    for init_file in init_files:
        if not os.path.exists(init_file):
            print(f"创建缺失的 {init_file}...")
            os.makedirs(os.path.dirname(init_file), exist_ok=True)
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write(f'"""{os.path.dirname(init_file)} 模块"""\n')
            print(f"✅ {init_file} 已创建")

def fix_utils_init():
    """修复utils/__init__.py文件"""
    print("\n🔧 修复 utils/__init__.py...")
    
    utils_init_content = '''"""
工具模块 - Utilities and Helper Functions

提供Token追踪、可视化、PGTree处理、LLM客户端等
核心工具和辅助功能。
"""

from .token_tracker import TokenTracker, OperationType

# 可选导入，避免依赖问题
try:
    from .token_visualizer import TokenUsageVisualizer
except ImportError:
    # matplotlib可能未安装，提供一个占位符
    class TokenUsageVisualizer:
        def __init__(self, *args, **kwargs):
            print("⚠️  TokenUsageVisualizer需要安装matplotlib")
        
        def __getattr__(self, name):
            def method(*args, **kwargs):
                print(f"TokenUsageVisualizer.{name} 需要安装matplotlib")
                return None
            return method

try:
    from .pg_tree_handler import PGTreeHandler, PGTreeNode, NodeStatus
except ImportError:
    print("⚠️  PGTreeHandler导入失败，请检查pg_tree_handler.py")

try:
    from .llm_client import LLMClient, ModelConfig, ModelProvider
except ImportError:
    print("⚠️  LLMClient导入失败，请检查llm_client.py")

__all__ = [
    "TokenTracker",
    "OperationType", 
    "TokenUsageVisualizer",
    "PGTreeHandler",
    "PGTreeNode", 
    "NodeStatus",
    "LLMClient",
    "ModelConfig",
    "ModelProvider"
]
'''
    
    try:
        with open('utils/__init__.py', 'w', encoding='utf-8') as f:
            f.write(utils_init_content)
        print("✅ utils/__init__.py 已修复")
    except Exception as e:
        print(f"❌ 修复utils/__init__.py失败: {e}")

def main():
    """主函数"""
    print("🚀 开始修复AutoPatent项目导入问题...")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('agents') or not os.path.exists('utils'):
        print("❌ 请在项目根目录运行此脚本")
        return False
    
    # 执行修复
    fix_relative_imports()
    create_missing_files()
    fix_utils_init()
    
    print("\n" + "=" * 50)
    print("🎉 导入问题修复完成！")
    print("\n💡 下一步:")
    print("1. 运行: python install_dependencies.py")
    print("2. 运行: python test_project.py")
    print("3. 设置API密钥并测试完整功能")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
