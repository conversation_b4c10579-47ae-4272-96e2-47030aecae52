# AutoPatent 智能体测试示例

本目录包含了AutoPatent项目中各个智能体的测试示例，帮助开发者了解和测试各个智能体的功能。

## 📁 文件结构

```
examples/
├── README.md                    # 本文档
├── examiner_agent_example.py    # ExaminerAgent 完整测试示例
├── examiner_quick_example.py    # ExaminerAgent 快速测试示例
├── planner_agent_example.py     # PlannerAgent 完整测试示例
├── planner_quick_example.py     # PlannerAgent 快速测试示例
├── writer_agent_example.py      # WriterAgent 完整测试示例
├── writer_quick_example.py      # WriterAgent 快速测试示例
├── agent_team_example.py        # 多智能体协作示例
└── example_usage.py             # 基础使用示例
```

## 🚀 快速开始

### 运行单个智能体示例

```bash
# 测试审查智能体
python examples/examiner_quick_example.py

# 测试规划智能体
python examples/planner_quick_example.py

# 测试写作智能体
python examples/writer_quick_example.py
```

### 运行完整功能示例

```bash
# 审查智能体完整功能测试
python examples/examiner_agent_example.py

# 规划智能体完整功能测试
python examples/planner_agent_example.py

# 写作智能体完整功能测试
python examples/writer_agent_example.py

# 多智能体协作演示
python examples/agent_team_example.py
```

## 🔍 ExaminerAgent 示例

### 快速示例 (examiner_quick_example.py)
- **功能**: 基本的专利审查功能测试
- **特点**: 简单快速，适合验证基本功能
- **输出**: 审查评分和发现的问题数量

### 完整示例 (examiner_agent_example.py)
- **功能**: 全面的审查功能测试
- **特点**: 包含RRAG检索、多section审查、统计信息
- **输出**: 详细的审查报告、RRAG结果、Token使用统计

**主要测试功能**:
- ✅ 单个section审查
- ✅ 全部section审查
- ✅ RRAG检索增强审查
- ✅ 审查统计信息
- ✅ Token使用追踪

## 🎯 PlannerAgent 示例

### 快速示例 (planner_quick_example.py)
- **功能**: 基本的专利规划功能测试
- **特点**: 快速验证规划能力
- **输出**: 规划策略、节点数量、预估信息

### 完整示例 (planner_agent_example.py)
- **功能**: 全面的规划功能测试
- **特点**: 多策略测试、自适应规划、优化功能
- **输出**: 详细的规划结果、概念分析、优化历史

**主要测试功能**:
- ✅ 基本规划功能
- ✅ 不同规划策略 (SIMPLIFIED, STANDARD, DETAILED)
- ✅ 自适应策略选择
- ✅ 规划优化
- ✅ 概念复杂度分析

## ✍️ WriterAgent 示例

### 快速示例 (writer_quick_example.py)
- **功能**: 基本的专利写作功能测试
- **特点**: 简单的abstract写作测试
- **输出**: 生成的内容和质量评分

### 完整示例 (writer_agent_example.py)
- **功能**: 全面的写作功能测试
- **特点**: 多类型、多风格、内容修订、质量优化
- **输出**: 不同类型和风格的写作结果

**主要测试功能**:
- ✅ 基本内容生成
- ✅ 不同写作类型 (GENERAL, TECHNICAL, LEGAL)
- ✅ 不同写作风格 (FORMAL, TECHNICAL, CONCISE)
- ✅ 内容修订功能
- ✅ 质量优化功能

## 🤖 多智能体协作示例 (agent_team_example.py)

**功能**: 演示完整的多智能体协作工作流
**流程**:
1. **规划阶段**: PlannerAgent 分析概念并创建规划
2. **写作阶段**: WriterAgent 根据规划生成各section内容
3. **审查阶段**: ExaminerAgent 审查生成的内容
4. **迭代改进**: 根据审查结果进行内容优化

**输出**: 完整的专利生成流程结果和统计信息

## 📊 示例输出说明

### ExaminerAgent 输出
```
✅ 审查成功完成
📊 总体评分: 7.22/10
🔍 发现问题: 7 个
⏱️  处理时间: 0.01秒
🎯 质量达标: 是
```

### PlannerAgent 输出
```
✅ 规划成功
📊 策略: standard
🔢 节点数: 5
⏱️  耗时: 0.10秒
📝 总预估字数: 2733
⏰ 总预估时间: 246分钟
```

### WriterAgent 输出
```
✅ 写作成功
📊 质量评分: 1.00/10
📏 内容长度: 22 字符
⏱️  写作时间: 0.20秒
```

## 🔧 自定义测试

### 修改测试概念
在示例文件中找到`concept`变量，替换为您想要测试的专利概念：

```python
concept = """
您的专利概念描述...
"""
```

### 调整测试参数
修改`options`字典来调整测试参数：

```python
options = {
    'technical_field': '您的技术领域',
    'patent_type': 'invention',
    'target_quality': 8.0
}
```

### 启用/禁用功能
通过构造函数参数控制智能体功能：

```python
# 启用RRAG的审查智能体
examiner = ExaminerAgent(patent_db=patent_db, enable_rrag=True)

# 启用自适应规划的规划智能体
planner = PlannerAgent(enable_adaptive_planning=True)

# 启用质量优化的写作智能体
writer = WriterAgent(enable_quality_optimization=True)
```

## 🐛 故障排除

### 常见问题

1. **导入错误**: 确保从项目根目录运行示例
2. **数据库错误**: 示例会自动创建临时数据库文件
3. **API错误**: 当前示例使用模拟响应，不需要真实API密钥
4. **内存不足**: 大型测试可能需要更多内存

### 调试技巧

1. **启用详细日志**:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **检查返回结果**:
```python
if not result['success']:
    print(f"错误: {result['error']}")
```

3. **查看统计信息**:
```python
stats = agent.get_statistics()
print(f"统计信息: {stats}")
```

## 📝 注意事项

1. **模拟模式**: 当前示例使用模拟的LLM响应，实际部署时需要配置真实的API
2. **数据库文件**: 示例会在当前目录创建临时数据库文件，可以安全删除
3. **性能**: 示例主要用于功能验证，不代表实际性能
4. **扩展性**: 可以基于这些示例开发更复杂的测试场景

## 🔗 相关文档

- [项目主README](../README.md)
- [智能体架构文档](../docs/PROJECT_STRUCTURE.md)
- [API使用指南](../docs/API_GUIDE.md)

## 🤝 贡献

欢迎提交新的测试示例或改进现有示例！请确保：
- 代码风格一致
- 包含适当的注释
- 测试覆盖主要功能
- 提供清晰的输出说明
