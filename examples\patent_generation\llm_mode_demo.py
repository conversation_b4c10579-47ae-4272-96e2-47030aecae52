#!/usr/bin/env python3
"""
LLM模式演示脚本

本脚本演示如何在模拟模式和真实API模式之间切换，
以及如何使用统一的LLM客户端进行专利生成。
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from autopatent.agents.planner_agent import PlannerAgent, PlanningStrategy
from autopatent.agents.writer_agent import WriterAgent, WriterType, WritingStyle
from autopatent.agents.examiner_agent import ExaminerAgent
from autopatent.database.patent_db import PatentDB


def set_llm_mode(use_mock: bool = True):
    """设置LLM模式"""
    # 设置环境变量
    os.environ['USE_MOCK_LLM'] = 'true' if use_mock else 'false'
    
    mode_name = "模拟模式" if use_mock else "真实API模式"
    print(f"🔧 LLM模式设置为: {mode_name}")
    
    if not use_mock:
        # 检查是否有真实API密钥
        api_key = os.getenv('DEEPSEEK_API_KEY')
        if not api_key:
            print("⚠️  警告: 未设置DEEPSEEK_API_KEY环境变量")
            print("   请在.env文件中设置或使用以下命令:")
            print("   export DEEPSEEK_API_KEY='your_api_key_here'")
            return False
        else:
            print(f"✅ 检测到API密钥: {api_key[:8]}...")
    
    return True


def test_llm_client_directly():
    """直接测试LLM客户端"""
    print("\n🧪 直接测试LLM客户端")
    print("-" * 40)
    
    from autopatent.utils.llm_client import LLMClient, ModelConfig, ModelProvider, Message
    
    # 测试模拟模式
    print("📝 测试模拟模式:")
    mock_config = ModelConfig(
        provider=ModelProvider.MOCK,
        model_name="deepseek-chat",
        api_key="mock_key",
        base_url="mock_url"
    )
    
    mock_client = LLMClient(mock_config)
    messages = [Message(role="user", content="请写一个关于智慧矿山的专利摘要")]
    
    try:
        response = mock_client.generate(messages)
        print(f"  ✅ 模拟响应成功")
        print(f"  📊 内容长度: {len(response.content)} 字符")
        print(f"  ⏱️  响应时间: {response.response_time:.2f}秒")
        print(f"  🔢 Token使用: {response.usage['total_tokens']}")
        print(f"  📄 内容预览: {response.content[:100]}...")
    except Exception as e:
        print(f"  ❌ 模拟响应失败: {e}")
    
    # 如果有API密钥，测试真实API
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if api_key:
        print(f"\n📡 测试真实API模式:")
        real_config = ModelConfig(
            provider=ModelProvider.DEEPSEEK,
            model_name="deepseek-chat",
            api_key=api_key,
            base_url="https://api.deepseek.com",
            max_tokens=500,
            temperature=0.7
        )
        
        real_client = LLMClient(real_config)
        
        try:
            response = real_client.generate(messages, max_tokens=200)
            print(f"  ✅ 真实API响应成功")
            print(f"  📊 内容长度: {len(response.content)} 字符")
            print(f"  ⏱️  响应时间: {response.response_time:.2f}秒")
            print(f"  🔢 Token使用: {response.usage['total_tokens']}")
            print(f"  📄 内容预览: {response.content[:100]}...")
        except Exception as e:
            print(f"  ❌ 真实API响应失败: {e}")
    else:
        print(f"\n⚠️  跳过真实API测试（未设置API密钥）")


def test_agent_with_mode(use_mock: bool = True):
    """测试智能体在指定模式下的工作"""
    mode_name = "模拟模式" if use_mock else "真实API模式"
    print(f"\n🤖 测试智能体 - {mode_name}")
    print("-" * 40)
    
    # 设置模式
    if not set_llm_mode(use_mock):
        return
    
    # 创建智能体
    writer = WriterAgent(WriterType.TECHNICAL, WritingStyle.FORMAL)
    
    # 创建简单的PGTree
    from autopatent.utils.pg_tree_handler import PGTreeHandler, PGTreeNode, NodeStatus
    
    pgtree = PGTreeHandler()
    node = PGTreeNode(
        node_id="abstract",
        section_name="abstract",
        status=NodeStatus.PENDING,
        priority="high",
        content=""
    )
    pgtree.add_node(node)
    
    # 测试概念
    concept = """
    一种基于元宇宙技术的智慧矿山安全监控系统，该系统集成了虚拟现实、
    人工智能、物联网等技术，实现矿山作业的实时监控和智能预警。
    """
    
    print(f"📝 概念: {concept[:50]}...")
    
    # 执行写作
    try:
        result = writer.process({
            'pgtree': pgtree,
            'section': 'abstract',
            'concept': concept,
            'technical_field': '智慧矿山技术'
        })
        
        if result['success']:
            content = result['content']
            quality = result['quality_score']
            
            print(f"✅ 写作成功")
            print(f"📊 质量评分: {quality:.2f}/10")
            print(f"📏 内容长度: {len(content)} 字符")
            print(f"📄 生成内容:")
            print("-" * 30)
            print(content)
            print("-" * 30)
            
            # 显示是否使用了模拟模式
            metadata = result.get('writing_metadata', {})
            if 'mock' in str(metadata).lower():
                print("🎭 使用了模拟响应")
            else:
                print("🌐 使用了真实API")
                
        else:
            print(f"❌ 写作失败: {result['error']}")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def demo_mode_switching():
    """演示模式切换"""
    print("🔄 LLM模式切换演示")
    print("=" * 50)
    
    # 首先测试LLM客户端
    test_llm_client_directly()
    
    # 测试模拟模式
    test_agent_with_mode(use_mock=True)
    
    # 如果有API密钥，测试真实API模式
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if api_key:
        test_agent_with_mode(use_mock=False)
    else:
        print(f"\n⚠️  跳过真实API模式测试")
        print("   要测试真实API模式，请设置DEEPSEEK_API_KEY环境变量")


def create_env_template():
    """创建.env模板文件"""
    env_template = """# AutoPatent 环境配置文件

# DeepSeek API配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# LLM模式配置
# 设置为 'false' 使用真实API，'true' 使用模拟模式
USE_MOCK_LLM=true

# 其他配置
LOG_LEVEL=INFO
"""
    
    env_file = Path('.env.template')
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(env_template)
    
    print(f"📄 已创建环境配置模板: {env_file}")
    print("   请复制为 .env 文件并填入您的API密钥")


def show_usage_instructions():
    """显示使用说明"""
    print("📖 使用说明")
    print("=" * 50)
    print()
    print("1. 模拟模式（默认）:")
    print("   - 不需要API密钥")
    print("   - 生成智能的模拟内容")
    print("   - 适合开发和测试")
    print("   - 设置: USE_MOCK_LLM=true")
    print()
    print("2. 真实API模式:")
    print("   - 需要DeepSeek API密钥")
    print("   - 生成真实的AI内容")
    print("   - 适合生产使用")
    print("   - 设置: USE_MOCK_LLM=false")
    print()
    print("3. 环境变量设置:")
    print("   - 在.env文件中设置配置")
    print("   - 或使用命令行: export DEEPSEEK_API_KEY='your_key'")
    print("   - 或使用命令行: export USE_MOCK_LLM='false'")
    print()
    print("4. 切换模式:")
    print("   - 修改.env文件中的USE_MOCK_LLM值")
    print("   - 或设置环境变量")
    print("   - 重新运行程序")


def main():
    """主函数"""
    print("🏔️  AutoPatent LLM模式演示系统")
    print("=" * 50)
    
    # 显示使用说明
    show_usage_instructions()
    
    # 创建环境配置模板
    if not Path('.env').exists() and not Path('.env.template').exists():
        create_env_template()
    
    # 演示模式切换
    demo_mode_switching()
    
    print(f"\n" + "=" * 50)
    print("✅ 演示完成")
    print()
    print("💡 提示:")
    print("   - 模拟模式适合开发和测试")
    print("   - 真实API模式适合生产使用")
    print("   - 可以通过环境变量随时切换模式")
    print("   - 所有智能体都会自动使用配置的模式")


if __name__ == "__main__":
    main()
