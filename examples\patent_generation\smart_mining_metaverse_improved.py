#!/usr/bin/env python3
"""
智慧矿山元宇宙技术专利生成 - 改进版本

本脚本使用改进的LLM客户端，支持模拟模式和真实API模式切换，
生成关于智慧矿山元宇宙技术的完整专利申请。
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from autopatent.agents.planner_agent import PlannerAgent, PlanningStrategy
from autopatent.agents.writer_agent import WriterAgent, WriterType, WritingStyle
from autopatent.agents.examiner_agent import ExaminerAgent
from autopatent.database.patent_db import PatentDB


def set_llm_mode(use_mock: bool = True):
    """设置LLM模式"""
    os.environ['USE_MOCK_LLM'] = 'true' if use_mock else 'false'
    mode_name = "模拟模式" if use_mock else "真实API模式"
    print(f"🔧 LLM模式设置为: {mode_name}")
    
    if not use_mock:
        api_key = os.getenv('DEEPSEEK_API_KEY')
        if not api_key:
            print("⚠️  警告: 未设置DEEPSEEK_API_KEY环境变量")
            return False
        else:
            print(f"✅ 检测到API密钥: {api_key[:8]}...")
    
    return True


class ImprovedPatentGenerator:
    """改进的专利生成器"""
    
    def __init__(self, use_mock: bool = True):
        """初始化生成器"""
        print("🏔️  初始化智慧矿山专利生成器...")
        
        # 设置LLM模式
        if not set_llm_mode(use_mock):
            print("❌ LLM模式设置失败")
            return
        
        # 创建数据库
        self.db_path = "smart_mining_patents_improved.db"
        self._setup_database()
        
        # 创建智能体
        self.planner = PlannerAgent(
            planning_strategy=PlanningStrategy.DETAILED,
            enable_adaptive_planning=True
        )
        
        self.writer = WriterAgent(
            writer_type=WriterType.GENERAL,  # 使用GENERAL类型，适合所有section
            writing_style=WritingStyle.FORMAL
        )
        
        with PatentDB(self.db_path) as patent_db:
            self.examiner = ExaminerAgent(
                patent_db=patent_db,
                enable_rrag=True
            )
        
        print("✅ 智能体团队初始化完成")
    
    def _setup_database(self):
        """设置专利数据库"""
        with PatentDB(self.db_path) as patent_db:
            existing_patents = [
                {
                    'patent_id': 'CN202110123456A',
                    'title': '一种基于虚拟现实的矿山安全培训系统',
                    'abstract': '本发明提供一种基于虚拟现实技术的矿山安全培训系统，通过构建虚拟矿山环境，实现沉浸式安全培训。系统包括VR头显设备、三维建模模块、交互控制模块和评估反馈模块，能够模拟各种矿山作业场景和应急情况。',
                    'technical_field': '矿山安全技术',
                    'quality_score': 7.8,
                    'filing_date': '2021-01-15T00:00:00'
                },
                {
                    'patent_id': 'CN202110234567A',
                    'title': '智能矿山数字孪生系统及其构建方法',
                    'abstract': '本发明涉及一种智能矿山数字孪生系统，通过物联网传感器和数字建模技术，构建矿山的数字化副本。系统实现了矿山设备状态的实时监控和预测性维护，包括数据采集层、数据处理层、模型构建层和应用服务层。',
                    'technical_field': '数字孪生技术',
                    'quality_score': 8.2,
                    'filing_date': '2021-02-20T00:00:00'
                },
                {
                    'patent_id': 'CN202110345678A',
                    'title': '基于区块链的矿山设备管理系统',
                    'abstract': '本发明提供一种基于区块链技术的矿山设备全生命周期管理系统，确保设备信息的可信性和可追溯性。系统采用分布式账本技术记录设备操作历史，包括设备注册、维护记录、故障处理和报废处置等全过程管理。',
                    'technical_field': '区块链技术',
                    'quality_score': 7.5,
                    'filing_date': '2021-03-10T00:00:00'
                }
            ]
            
            for patent in existing_patents:
                patent_db.add_patent(patent)
            
            print(f"📚 已添加 {len(existing_patents)} 个相关专利到数据库")
    
    def generate_patent(self, concept: str, options: dict = None) -> dict:
        """生成专利的主流程"""
        if not options:
            options = {}
        
        start_time = datetime.now()
        workflow_id = f"smart_mining_improved_{int(time.time())}"
        
        print(f"\n🚀 开始生成专利 - 工作流ID: {workflow_id}")
        print("=" * 80)
        
        try:
            # 第一阶段：规划
            print("\n🎯 第一阶段：智能规划")
            print("-" * 40)
            
            planning_result = self.planner.process({
                'concept': concept,
                'options': options,
                'workflow_id': workflow_id
            })
            
            if not planning_result['success']:
                return {'success': False, 'error': f"规划失败: {planning_result['error']}"}
            
            pgtree_plan = planning_result['pgtree']
            print(f"✅ 规划完成 - 策略: {pgtree_plan.strategy.value}")
            print(f"📊 生成 {len(pgtree_plan.structure)} 个section")
            
            # 第二阶段：写作
            print(f"\n✍️  第二阶段：智能写作")
            print("-" * 40)
            
            # 转换为PGTreeHandler格式
            from autopatent.utils.pg_tree_handler import PGTreeHandler, PGTreeNode, NodeStatus
            
            pgtree_handler = PGTreeHandler()
            
            # 添加节点
            for section_id, section_plan in pgtree_plan.structure.items():
                node = PGTreeNode(
                    node_id=section_id,
                    section_name=section_plan.section_name,
                    status=NodeStatus.PENDING,
                    priority=section_plan.priority.value,
                    content="",
                    requirements=section_plan.requirements
                )
                pgtree_handler.add_node(node)
            
            # 写作各个section
            sections_to_write = ['abstract', 'background', 'summary', 'detailed_description', 'claims']
            writing_results = {}
            
            for section in sections_to_write:
                if section in pgtree_handler.nodes:
                    print(f"  📝 写作 {section}...")
                    
                    writing_result = self.writer.process({
                        'pgtree': pgtree_handler,
                        'section': section,
                        'concept': concept,
                        'technical_field': options.get('technical_field', '智慧矿山技术'),
                        'workflow_id': workflow_id
                    })
                    
                    if writing_result['success']:
                        pgtree_handler = writing_result['pgtree']
                        writing_results[section] = writing_result
                        quality = writing_result['quality_score']
                        content_length = len(writing_result['content'])
                        print(f"    ✅ {section} 完成 - 质量: {quality:.2f}/10, 长度: {content_length} 字符")
                    else:
                        print(f"    ❌ {section} 失败: {writing_result['error']}")
            
            # 第三阶段：审查
            print(f"\n🔍 第三阶段：智能审查")
            print("-" * 40)
            
            with PatentDB(self.db_path) as patent_db:
                examiner = ExaminerAgent(patent_db=patent_db, enable_rrag=True)
                examination_result = examiner.process({
                    'pgtree': pgtree_handler,
                    'section': 'all',
                    'examination_type': 'substantive',
                    'workflow_id': workflow_id
                })
            
            if examination_result['success']:
                overall_score = examination_result['overall_score']
                issues_count = len(examination_result['issues'])
                
                print(f"✅ 审查完成")
                print(f"📊 总体评分: {overall_score:.2f}/10")
                print(f"⚠️  发现问题: {issues_count} 个")
                
                # 显示主要问题
                if examination_result['issues']:
                    print(f"\n📋 主要问题:")
                    for i, issue in enumerate(examination_result['issues'][:5], 1):
                        print(f"  {i}. [{issue['issue_type']}] {issue['description']}")
                
                # RRAG结果
                if examination_result.get('rrag_result'):
                    rrag = examination_result['rrag_result']
                    print(f"\n🔬 RRAG分析:")
                    print(f"  新颖性评分: {rrag['novelty_analysis']['novelty_score']:.2f}/10")
                    print(f"  相似专利数: {len(rrag['retrieved_patents'])}")
                    print(f"  风险评估: {rrag['risk_assessment']}")
            
            # 生成最终结果
            total_time = (datetime.now() - start_time).total_seconds()
            
            patent_content = {}
            for node_id, node in pgtree_handler.nodes.items():
                if node.content:
                    patent_content[node_id] = node.content
            
            return {
                'success': True,
                'workflow_id': workflow_id,
                'patent_content': patent_content,
                'planning_result': planning_result,
                'writing_results': writing_results,
                'examination_result': examination_result,
                'metadata': {
                    'total_processing_time': total_time,
                    'sections_generated': len(writing_results),
                    'final_quality_score': examination_result.get('overall_score', 0),
                    'concept_length': len(concept),
                    'technical_field': options.get('technical_field', '智慧矿山技术'),
                    'llm_mode': 'mock' if os.getenv('USE_MOCK_LLM', 'true').lower() == 'true' else 'real'
                }
            }
            
        except Exception as e:
            print(f"❌ 专利生成过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': str(e)}


def main():
    """主函数"""
    print("🏔️  智慧矿山元宇宙技术专利生成系统 - 改进版")
    print("=" * 80)
    
    # 智慧矿山元宇宙专利概念
    concept = """
    一种基于元宇宙技术的智慧矿山综合管理系统，该系统融合了虚拟现实(VR)、增强现实(AR)、
    数字孪生、人工智能、物联网、区块链等前沿技术，构建矿山作业的沉浸式虚拟环境。
    
    系统的核心创新包括：
    
    1. 矿山元宇宙空间构建技术
       - 基于激光雷达和摄影测量的高精度三维重建
       - 实时动态环境更新和渲染优化
       - 多尺度空间建模（从设备级到矿区级）
       - 支持多用户同时在线的虚拟协作空间
    
    2. 沉浸式人机交互技术
       - 多模态交互界面（手势、语音、眼动、脑机接口）
       - 触觉反馈和力觉模拟系统
       - 虚拟协作和远程操控技术
       - 自然语言处理和智能对话系统
    
    3. 智能决策支持系统
       - 基于数字孪生的实时状态监测
       - AI驱动的预测性维护和风险评估
       - 智能调度和资源优化算法
       - 机器学习驱动的异常检测和预警
    
    4. 区块链安全保障机制
       - 分布式身份认证和权限管理
       - 操作记录的不可篡改存储
       - 智能合约驱动的自动化流程
       - 多方协作的信任机制
    
    5. 跨平台兼容性技术
       - 支持PC、移动设备、VR/AR头显等多终端
       - 云边协同计算架构
       - 5G/6G网络优化传输
       - 跨设备数据同步和状态保持
    
    应用场景包括：矿山安全培训、远程设备操控、应急演练、生产调度、
    设备维护、地质勘探、环境监测、人员定位、智能巡检等。
    该系统能够显著提升矿山作业的安全性、效率和智能化水平，
    为矿山行业的数字化转型提供全面的技术解决方案。
    """
    
    # 配置选项
    options = {
        'technical_field': '智慧矿山与元宇宙技术',
        'patent_type': 'invention',
        'target_quality': 8.0
    }
    
    # 检查是否有真实API密钥
    api_key = os.getenv('DEEPSEEK_API_KEY')
    use_mock = not bool(api_key)
    
    if use_mock:
        print("🎭 使用模拟模式（未检测到API密钥）")
    else:
        print("🌐 使用真实API模式")
    
    # 创建生成器并生成专利
    generator = ImprovedPatentGenerator(use_mock=use_mock)
    result = generator.generate_patent(concept, options)
    
    # 处理结果
    if result['success']:
        print(f"\n🎉 专利生成成功！")
        print("=" * 80)
        
        metadata = result['metadata']
        print(f"📊 最终质量评分: {metadata['final_quality_score']:.2f}/10")
        print(f"⏱️  总处理时间: {metadata['total_processing_time']:.2f}秒")
        print(f"📝 生成section数: {metadata['sections_generated']}")
        print(f"🔧 LLM模式: {metadata['llm_mode']}")
        print(f"🔬 技术领域: {metadata['technical_field']}")
        
        # 保存完整结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"smart_mining_metaverse_improved_{timestamp}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 完整结果已保存到: {output_file}")
        
        # 显示专利内容预览
        print(f"\n📄 专利内容预览:")
        print("-" * 80)
        
        patent_content = result['patent_content']
        for section, content in patent_content.items():
            print(f"\n【{section.upper()}】")
            # 显示更多内容
            preview = content[:800] + "..." if len(content) > 800 else content
            print(preview)
        
        # 显示统计信息
        print(f"\n📊 详细统计:")
        print("-" * 40)
        total_words = 0
        total_chars = 0
        for section, content in patent_content.items():
            word_count = len(content.split())
            char_count = len(content)
            total_words += word_count
            total_chars += char_count
            print(f"  {section}: {word_count} 词, {char_count} 字符")
        
        print(f"\n  总计: {total_words} 词, {total_chars} 字符")
        
        # 显示改进建议
        examination = result['examination_result']
        if examination.get('issues'):
            print(f"\n🔧 改进建议:")
            for i, issue in enumerate(examination['issues'][:3], 1):
                if issue.get('suggestion'):
                    print(f"  {i}. {issue['suggestion']}")
        
    else:
        print(f"\n❌ 专利生成失败: {result['error']}")


if __name__ == "__main__":
    main()
