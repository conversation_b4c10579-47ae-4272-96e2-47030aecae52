# AutoPatent - 多智能体专利自动生成系统

基于论文 [AutoPatent: A Multi-Agent Framework for Automatic Patent Generation](https://doi.org/10.48550/arXiv.2412.09796) 的Python实现。

## 🌟 功能特性

- **多智能体架构**：规划智能体(Planner)、撰写智能体(Writer)、审查智能体(Examiner)
- **PGTree结构**：专利生成树，管理专利各部分的结构化生成
- **RRAG机制**：检索增强生成用于审查，提高专利质量
- **现代化CLI**：基于typer和rich的用户友好命令行界面
- **专利数据库**：支持专利存储、检索和相似性搜索
- **令牌追踪**：监控LLM API使用情况和成本

## 🚀 快速开始

### 环境要求

- Python 3.8+
- DeepSeek API密钥

### 使用 uv 安装

```bash
# 安装 uv (如果还没安装)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 克隆项目
git clone <your-repo-url>
cd autopatent

# 创建虚拟环境并安装依赖
uv sync

# 安装开发依赖(可选)
uv sync --extra dev
```

### 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑.env文件，添加您的API密钥
echo "DEEPSEEK_API_KEY=your_api_key_here" >> .env
```

### 基本使用

```bash
# 演示模式
python autopatent.py run --demo

# 交互式模式
python autopatent.py run --interactive

# 命令行模式
python autopatent.py run --concept "一种基于AI的智能推荐系统" --field "人工智能" --output result.json

# 运行测试
python autopatent.py test

# 生成可视化报告
python autopatent.py visualize --input data.json --output reports
```

## 📖 详细使用

### 1. 交互式模式

```bash
python autopatent.py run --interactive
```

交互式模式将引导您完成：

- 输入专利概念
- 选择技术领域
- 配置专利类型和参数
- 实时查看生成进度

### 2. 命令行模式

```bash
# 基本生成（演示模式）
python autopatent.py run --demo

# 指定概念生成
python autopatent.py run --concept "您的专利概念描述" --field "技术领域"

# 完整参数
python autopatent.py run \
  --concept "一种智能家居能耗管理系统" \
  --field "智能家居技术" \
  --output patent_result.json
```

### 3. Python API

```python
from autopatent.examples.simple_demo import SimpleCoordinator

# 初始化协调器
coordinator = SimpleCoordinator()

# 准备概念和选项
concept = "您的专利概念描述"
options = {
    'technical_field': '技术领域',
    'patent_type': 'invention'
}

# 生成专利
result = coordinator.generate_patent(concept, options)

if result['success']:
    print("专利生成成功！")
    print(f"质量评分: {result.get('quality_score', 0):.1f}/10")
    # 处理结果...
else:
    print(f"生成失败：{result['error']}")
```

## 🛠️ 开发

### 开发环境设置

```bash
# 安装开发依赖
uv sync --extra dev

# 安装pre-commit hooks
uv run pre-commit install

# 运行测试
make test

# 代码格式化
make format

# 代码检查
make lint
```

### 项目结构

```text
autopatent/
├── agents/              # 智能体模块
│   ├── base_agent.py   # 基础智能体类
│   ├── planner_agent.py # 规划智能体
│   ├── writer_agent.py  # 撰写智能体
│   └── examiner_agent.py # 审查智能体
├── coordinator/         # 协调器
│   └── coordinator.py  # 主协调器
├── database/           # 数据库模块
│   └── patent_db.py   # 专利数据库
├── utils/              # 工具模块
│   ├── llm_client.py  # LLM客户端
│   └── token_tracker.py # 令牌追踪
├── examples/           # 示例代码
│   └── simple_demo.py # 简化演示
├── cli.py             # 命令行界面
└── __init__.py        # 包初始化
autopatent.py          # 主启动器
config.py             # 配置管理
```

### 运行测试

```bash
# 运行所有测试
python autopatent.py test

# 或者直接使用pytest（如果已安装）
pytest

# 运行特定测试
pytest tests/test_planner_agent.py

# 生成覆盖率报告
pytest --cov=autopatent --cov-report=html
```

## 🔧 配置

### 环境变量

```bash
# 必需
DEEPSEEK_API_KEY=your_deepseek_api_key

# 可选
DATABASE_PATH=patents.db
LOG_LEVEL=INFO
MAX_ITERATIONS=3
QUALITY_THRESHOLD=7.0
```

### 高级配置

编辑 `config.py` 以自定义：

- LLM模型参数
- 工作流设置
- 专利结构定义
- 数据库配置

## 📊 监控和统计

### 查看系统状态

```bash
python autopatent.py tools --organize
```

### 令牌使用统计

```python
from utils.token_tracker import TokenTracker

tracker = TokenTracker()
# 使用后...
print(tracker.get_detailed_usage())
print(tracker.estimate_cost())
```

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 基于论文：[AutoPatent: A Multi-Agent Framework for Automatic Patent Generation](https://doi.org/10.48550/arXiv.2412.09796)
- 感谢 DeepSeek 提供的大模型API支持
- 感谢开源社区的各种工具和库

## 📞 联系

如有问题或建议，请：

- 创建 Issue
- 发送邮件到：<<EMAIL>>
- 访问项目主页：<https://github.com/longwarriors/draftPatents>