#!/usr/bin/env python3
"""
自动安装AutoPatent项目依赖的脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主函数"""
    print("🚀 开始安装AutoPatent项目依赖...")
    
    # 核心依赖包
    core_packages = [
        "pydantic>=2.0.0",
        "python-dotenv>=1.0.0", 
        "numpy>=1.24.0",
        "openai>=1.0.0",
        "httpx>=0.24.0",
        "aiohttp>=3.8.0",
        "requests>=2.28.0"
    ]
    
    # 可选依赖包（用于可视化）
    optional_packages = [
        "matplotlib>=3.5.0",
        "plotly>=5.0.0"
    ]
    
    print(f"📦 安装 {len(core_packages)} 个核心依赖包...")
    
    failed_packages = []
    
    for package in core_packages:
        print(f"正在安装: {package}")
        if install_package(package):
            print(f"✅ {package} 安装成功")
        else:
            print(f"❌ {package} 安装失败")
            failed_packages.append(package)
    
    print(f"\n📊 可选依赖包安装（用于数据可视化）...")
    
    for package in optional_packages:
        print(f"正在安装: {package}")
        if install_package(package):
            print(f"✅ {package} 安装成功")
        else:
            print(f"⚠️  {package} 安装失败（可选，不影响核心功能）")
    
    if failed_packages:
        print(f"\n❌ 以下核心包安装失败: {', '.join(failed_packages)}")
        print("请手动安装这些包或检查网络连接")
        return False
    else:
        print(f"\n🎉 所有核心依赖安装完成！")
        print("\n💡 下一步:")
        print("1. 设置环境变量: DEEPSEEK_API_KEY=your_api_key")
        print("2. 运行测试: python test_project.py")
        print("3. 运行演示: python run_autopatent.py --demo")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
