"""
PlannerAgent 完整使用示例
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from autopatent.agents.planner_agent import PlannerAgent, PlanningStrategy, PatentType
from autopatent.utils.pg_tree_handler import PGTreeHandler


def test_basic_planning():
    """测试基本规划功能"""
    print("=== PlannerAgent 基本规划测试 ===")
    
    # 创建规划智能体
    planner = PlannerAgent(
        planning_strategy=PlanningStrategy.STANDARD,
        enable_adaptive_planning=True
    )
    
    # 准备专利概念
    concept = """
    一种基于深度学习的智能交通信号控制系统，该系统能够实时分析交通流量数据，
    动态调整信号灯时序，以优化交通流量和减少拥堵。
    
    系统的核心创新包括：
    1. 多模态交通数据融合技术，整合摄像头、雷达、传感器数据
    2. 深度强化学习算法，实现自适应信号控制策略
    3. 分布式协调机制，支持多路口协同优化
    4. 实时预测模型，能够预测未来15分钟的交通状况
    """
    
    # 规划选项
    options = {
        'technical_field': '智能交通系统',
        'patent_type': 'invention',
        'priority_sections': ['claims', 'summary', 'detailed_description'],
        'target_quality': 8.0,
        'enable_optimization': True
    }
    
    print(f"📝 专利概念: {concept[:100]}...")
    print(f"🔬 技术领域: {options['technical_field']}")
    print(f"📋 专利类型: {options['patent_type']}")
    
    # 执行规划
    print("\n🚀 开始执行规划...")
    result = planner.process({
        'concept': concept,
        'options': options,
        'workflow_id': 'planner_example_001'
    })
    
    # 显示规划结果
    if result['success']:
        print("✅ 规划成功完成")
        
        pgtree = result['pgtree']
        planning_metadata = result['planning_metadata']
        
        print(f"📊 规划策略: {planning_metadata['strategy_used']}")
        print(f"🎯 专利类型: {planning_metadata['patent_type']}")
        print(f"⏱️  规划时间: {planning_metadata['processing_time']:.2f}秒")
        print(f"🔢 节点总数: {len(pgtree.nodes)}")
        
        # 显示PGTree结构
        print(f"\n📋 PGTree结构:")
        for node_id, node in pgtree.nodes.items():
            priority_icon = "🔴" if node.priority == "critical" else "🟡" if node.priority == "high" else "🟢"
            status_icon = "✅" if node.status.value == "completed" else "⏳" if node.status.value == "in_progress" else "⭕"
            print(f"  {priority_icon} {status_icon} {node_id}: {node.section_name}")
            if hasattr(node, 'dependencies') and node.dependencies:
                print(f"    📎 依赖: {', '.join(node.dependencies)}")
        
        # 显示规划建议
        if result.get('recommendations'):
            print(f"\n💭 规划建议:")
            for i, rec in enumerate(result['recommendations'][:5], 1):
                print(f"  {i}. {rec}")
        
        # 显示分析结果
        if planning_metadata.get('concept_analysis'):
            analysis = planning_metadata['concept_analysis']
            print(f"\n🔍 概念分析:")
            print(f"  复杂度评分: {analysis.get('complexity_score', 0):.1f}/10")
            print(f"  技术新颖性: {analysis.get('estimated_novelty', 0):.2f}")
            print(f"  关键技术点: {len(analysis.get('key_technologies', []))}")
            
            if analysis.get('key_technologies'):
                print(f"  主要技术:")
                for tech in analysis['key_technologies'][:3]:
                    print(f"    • {tech}")
    else:
        print(f"❌ 规划失败: {result['error']}")
    
    return result


def test_different_strategies():
    """测试不同规划策略"""
    print("\n=== 不同规划策略测试 ===")
    
    concept = "一种智能手机电池管理芯片，具有快速充电和智能功耗控制功能。"
    
    strategies = [
        PlanningStrategy.SIMPLIFIED,
        PlanningStrategy.STANDARD,
        PlanningStrategy.DETAILED
    ]
    
    for strategy in strategies:
        print(f"\n🎯 测试策略: {strategy.value}")
        
        planner = PlannerAgent(planning_strategy=strategy)
        
        result = planner.process({
            'concept': concept,
            'options': {
                'technical_field': '电子技术',
                'patent_type': 'invention'
            }
        })
        
        if result['success']:
            pgtree = result['pgtree']
            metadata = result['planning_metadata']
            
            print(f"  📊 节点数量: {len(pgtree.nodes)}")
            print(f"  ⏱️  处理时间: {metadata['processing_time']:.2f}秒")
            print(f"  🎯 优化次数: {metadata.get('optimization_iterations', 0)}")
        else:
            print(f"  ❌ 失败: {result['error']}")


def test_adaptive_planning():
    """测试自适应规划"""
    print("\n=== 自适应规划测试 ===")
    
    # 测试不同复杂度的概念
    test_cases = [
        {
            'name': '简单概念',
            'concept': '一种新型的手机支架，具有可调节角度功能。',
            'expected_strategy': 'simplified'
        },
        {
            'name': '中等复杂度概念',
            'concept': '一种基于机器学习的图像识别系统，用于自动分类产品图片。',
            'expected_strategy': 'standard'
        },
        {
            'name': '复杂概念',
            'concept': '''
            一种基于量子计算的加密通信系统，集成了量子密钥分发、量子纠错、
            多层量子协议栈、量子网络路由算法、以及与经典网络的混合架构。
            系统支持大规模量子网络部署，具有容错能力和可扩展性。
            ''',
            'expected_strategy': 'detailed'
        }
    ]
    
    planner = PlannerAgent(enable_adaptive_planning=True)
    
    for case in test_cases:
        print(f"\n🧪 测试: {case['name']}")
        print(f"📝 概念: {case['concept'][:80]}...")
        
        result = planner.process({
            'concept': case['concept'],
            'options': {'technical_field': '通用技术'}
        })
        
        if result['success']:
            actual_strategy = result['planning_metadata']['strategy_used']
            print(f"  🎯 选择策略: {actual_strategy}")
            print(f"  📊 节点数量: {len(result['pgtree'].nodes)}")
            
            # 验证策略选择是否合理
            if case['expected_strategy'] in actual_strategy.lower():
                print(f"  ✅ 策略选择合理")
            else:
                print(f"  ⚠️  策略选择可能需要调整")
        else:
            print(f"  ❌ 失败: {result['error']}")


def test_planning_optimization():
    """测试规划优化功能"""
    print("\n=== 规划优化测试 ===")
    
    planner = PlannerAgent(
        planning_strategy=PlanningStrategy.STANDARD,
        enable_optimization=True
    )
    
    concept = """
    一种智能农业灌溉系统，结合物联网传感器、气象数据、土壤分析、
    作物生长模型和机器学习算法，实现精准灌溉控制。
    """
    
    options = {
        'technical_field': '智能农业',
        'patent_type': 'invention',
        'enable_optimization': True,
        'target_quality': 8.5
    }
    
    print(f"📝 概念: {concept[:100]}...")
    
    result = planner.process({
        'concept': concept,
        'options': options,
        'workflow_id': 'optimization_test_001'
    })
    
    if result['success']:
        metadata = result['planning_metadata']
        
        print(f"✅ 优化完成")
        print(f"🔄 优化迭代: {metadata.get('optimization_iterations', 0)}")
        print(f"📈 优化改进: {metadata.get('optimization_improvements', [])}")
        print(f"⏱️  总处理时间: {metadata['processing_time']:.2f}秒")
        
        # 显示优化前后对比
        if metadata.get('optimization_history'):
            history = metadata['optimization_history']
            print(f"\n📊 优化历史:")
            for i, step in enumerate(history):
                print(f"  步骤 {i+1}: {step.get('description', 'N/A')}")
                print(f"    评分: {step.get('score', 0):.2f}")
    else:
        print(f"❌ 优化失败: {result['error']}")


def main():
    """主函数 - 运行所有测试"""
    print("🚀 PlannerAgent 完整测试套件")
    print("=" * 50)
    
    try:
        # 基本规划测试
        basic_result = test_basic_planning()
        
        # 不同策略测试
        test_different_strategies()
        
        # 自适应规划测试
        test_adaptive_planning()
        
        # 规划优化测试
        test_planning_optimization()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成")
        
        # 显示统计信息
        if basic_result and basic_result['success']:
            planner = PlannerAgent()
            stats = planner.get_planning_statistics()
            print(f"\n📊 规划统计:")
            print(f"  总规划次数: {stats.get('total_plannings', 0)}")
            print(f"  平均处理时间: {stats.get('average_planning_time', 0):.2f}秒")
            print(f"  平均节点数: {stats.get('average_nodes_created', 0):.1f}")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
