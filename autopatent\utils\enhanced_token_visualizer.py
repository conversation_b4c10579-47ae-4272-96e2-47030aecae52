"""
增强版Token使用可视化工具 - 中英混合UI界面，支持人民币计费
"""

from typing import Dict, Any, List, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import pandas as pd
from pathlib import Path
import os
import platform
import json
import glob

try:
    from .token_tracker import TokenTracker, OperationType
except ImportError:
    # 兼容性导入
    try:
        from token_tracker import TokenTracker, OperationType
    except ImportError:
        print("⚠️  无法导入TokenTracker，某些功能可能不可用")
        TokenTracker = None
        OperationType = None


class EnhancedTokenVisualizer:
    """增强版Token使用可视化器 - 中英混合界面"""

    # 汇率配置
    USD_TO_CNY_RATE = 7.2  # 美元转人民币汇率，可根据实际情况调整

    # 中英文标签映射
    LABELS = {
        'title': {
            'overview': 'Token使用概览 (Token Usage Overview)',
            'trends': 'Token使用趋势分析 (Usage Trend Analysis)',
            'efficiency': '效率分析 (Efficiency Analysis)',
            'cost': '成本分析 (Cost Analysis)',
            'comparison': '会话对比 (Session Comparison)'
        },
        'charts': {
            'token_distribution': 'Token类型分布 (Token Type Distribution)',
            'operation_distribution': '操作类型分布 (Operation Distribution)',
            'agent_usage': 'Agent调用次数 (Agent Call Count)',
            'model_usage': 'Model使用统计 (Model Usage)',
            'token_trend': 'Token使用趋势 (Token Usage Trend)',
            'cost_trend': '成本趋势 (Cost Trend)',
            'api_calls': 'API调用趋势 (API Call Trend)',
            'processing_speed': 'Token处理速度 (Processing Speed)',
            'response_time': '平均响应时间 (Avg Response Time)',
            'cost_distribution': '成本分布 (Cost Distribution)'
        },
        'metrics': {
            'total_tokens': 'Total Tokens',
            'total_cost_usd': 'Total Cost (USD)',
            'total_cost_cny': '总成本 (￥)',
            'api_calls': 'API Calls',
            'success_rate': 'Success Rate',
            'input_tokens': 'Input Tokens',
            'output_tokens': 'Output Tokens',
            'reasoning_tokens': 'Reasoning Tokens'
        },
        'units': {
            'tokens_per_sec': 'tokens/秒',
            'seconds': '秒',
            'count': '次数',
            'usd': 'USD',
            'cny': '￥'
        }
    }

    def __init__(self, token_tracker: Optional[TokenTracker] = None):
        """
        初始化增强版可视化器

        Args:
            token_tracker: TokenTracker实例
        """
        self.token_tracker = token_tracker
        self._session_data = None
        
        # 设置中文字体
        self._setup_chinese_font()
        
        # 设置样式
        sns.set_style("whitegrid")
        sns.set_palette("husl")
        
        # 设置matplotlib参数
        plt.rcParams['figure.facecolor'] = 'white'
        plt.rcParams['axes.facecolor'] = 'white'
        plt.rcParams['savefig.facecolor'] = 'white'
        plt.rcParams['savefig.dpi'] = 300

    def _setup_chinese_font(self):
        """设置中文字体支持"""
        system = platform.system()
        
        if system == 'Windows':
            font_list = ['Microsoft YaHei', 'SimHei', 'SimSun']
        elif system == 'Darwin':  # macOS
            font_list = ['Arial Unicode MS', 'Heiti TC', 'STHeiti']
        else:  # Linux
            font_list = ['WenQuanYi Micro Hei', 'Droid Sans Fallback']
        
        # 添加通用字体
        font_list.extend(['DejaVu Sans', 'Arial', 'sans-serif'])
        
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = font_list
        plt.rcParams['axes.unicode_minus'] = False

    def _convert_usd_to_cny(self, usd_amount: float) -> float:
        """将美元转换为人民币"""
        return usd_amount * self.USD_TO_CNY_RATE

    def _format_cost(self, cost_usd: float, show_both: bool = True) -> str:
        """格式化成本显示"""
        if show_both:
            cost_cny = self._convert_usd_to_cny(cost_usd)
            return f"${cost_usd:.4f} (￥{cost_cny:.4f})"
        else:
            cost_cny = self._convert_usd_to_cny(cost_usd)
            return f"￥{cost_cny:.4f}"

    @classmethod
    def from_export_file(cls, export_file_path: str) -> 'EnhancedTokenVisualizer':
        """从导出文件创建可视化器实例"""
        instance = cls()
        instance.load_from_export(export_file_path)
        return instance

    def load_from_export(self, export_file_path: str) -> None:
        """从导出文件加载数据"""
        if not os.path.exists(export_file_path):
            raise FileNotFoundError(f"文件不存在: {export_file_path}")

        file_extension = os.path.splitext(export_file_path)[1].lower()

        if file_extension == '.json':
            with open(export_file_path, 'r', encoding='utf-8') as f:
                self._session_data = json.load(f)
        elif file_extension in ['.xlsx', '.xls']:
            self._session_data = {}
            excel_data = pd.ExcelFile(export_file_path)
            
            if 'Session统计' in excel_data.sheet_names:
                session_df = pd.read_excel(excel_data, 'Session统计')
                self._session_data["session统计"] = session_df.iloc[0].to_dict() if not session_df.empty else {}
            
            if '使用记录' in excel_data.sheet_names:
                records_df = pd.read_excel(excel_data, '使用记录')
                self._session_data["原始记录"] = records_df.to_dict('records') if not records_df.empty else []
        elif file_extension == '.csv':
            records_df = pd.read_csv(export_file_path)
            self._session_data = {"原始记录": records_df.to_dict('records')}
        else:
            raise ValueError(f"不支持的文件格式: {file_extension}")

        print(f"✅ 已从文件 {export_file_path} 加载数据")

    def get_usage_data(self) -> Dict[str, Any]:
        """获取使用数据"""
        if self.token_tracker:
            return self.token_tracker.get_usage()
        elif self._session_data and "session统计" in self._session_data:
            return self._session_data["session统计"]
        else:
            raise ValueError("没有可用的使用数据")

    def create_enhanced_dashboard(self, output_dir: str = "reports") -> str:
        """创建增强版dashboard"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 生成各种图表
        self._create_enhanced_overview_charts(output_path)
        self._create_enhanced_trend_charts(output_path)
        self._create_enhanced_efficiency_charts(output_path)
        self._create_enhanced_cost_charts(output_path)

        # 生成HTML报告
        html_path = output_path / "enhanced_token_dashboard.html"
        self._generate_enhanced_html_report(html_path, output_path)

        return str(html_path)

    def _create_enhanced_overview_charts(self, output_path: Path):
        """创建增强版概览图表"""
        try:
            session_stats = self.get_usage_data()
        except ValueError as e:
            print(f"获取使用数据失败: {e}")
            return

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(self.LABELS['title']['overview'], fontsize=18, fontweight='bold')

        # 1. Token类型分布
        token_data = {
            self.LABELS['metrics']['input_tokens']: session_stats.get('total_input_tokens', 0),
            self.LABELS['metrics']['output_tokens']: session_stats.get('total_output_tokens', 0),
            self.LABELS['metrics']['reasoning_tokens']: session_stats.get('total_reasoning_tokens', 0)
        }

        if sum(token_data.values()) > 0:
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
            wedges, texts, autotexts = ax1.pie(
                token_data.values(), 
                labels=token_data.keys(), 
                autopct='%1.1f%%',
                colors=colors,
                startangle=90
            )
            ax1.set_title(self.LABELS['charts']['token_distribution'], fontsize=12, pad=20)

        # 2. 操作类型分布
        operations = session_stats.get('operations', {})
        if operations:
            # 翻译操作名称
            translated_ops = {}
            for op, count in operations.items():
                if op == 'planning':
                    translated_ops['规划 (Planning)'] = count
                elif op == 'writing':
                    translated_ops['写作 (Writing)'] = count
                elif op == 'examining':
                    translated_ops['审查 (Examining)'] = count
                else:
                    translated_ops[op] = count
            
            ax2.pie(translated_ops.values(), labels=translated_ops.keys(), autopct='%1.1f%%')
            ax2.set_title(self.LABELS['charts']['operation_distribution'], fontsize=12, pad=20)

        # 3. Agent使用统计
        agents = session_stats.get('agents_used', {})
        if agents:
            bars = ax3.bar(agents.keys(), agents.values(), color='skyblue')
            ax3.set_title(self.LABELS['charts']['agent_usage'], fontsize=12, pad=20)
            ax3.set_ylabel(self.LABELS['units']['count'])
            ax3.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height)}', ha='center', va='bottom')

        # 4. Model使用统计
        models = session_stats.get('models_used', {})
        if models:
            bars = ax4.bar(models.keys(), models.values(), color='lightcoral')
            ax4.set_title(self.LABELS['charts']['model_usage'], fontsize=12, pad=20)
            ax4.set_ylabel(self.LABELS['units']['count'])
            ax4.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height)}', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig(output_path / 'enhanced_overview.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _create_enhanced_trend_charts(self, output_path: Path):
        """创建增强版趋势图表"""
        # 这里可以添加更详细的趋势分析
        # 由于篇幅限制，先创建基础版本
        try:
            session_stats = self.get_usage_data()
            
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
            fig.suptitle(self.LABELS['title']['trends'], fontsize=16, fontweight='bold')
            
            # 模拟趋势数据（实际应用中从详细记录中提取）
            hours = list(range(24))
            token_usage = [session_stats.get('total_tokens', 0) * (0.5 + 0.5 * (h % 12) / 12) for h in hours]
            cost_usage = [session_stats.get('total_cost', 0) * (0.5 + 0.5 * (h % 12) / 12) for h in hours]
            
            # Token使用趋势
            ax1.plot(hours, token_usage, marker='o', linewidth=2, color='#4ECDC4')
            ax1.set_title(self.LABELS['charts']['token_trend'])
            ax1.set_ylabel('Token Count')
            ax1.grid(True, alpha=0.3)
            
            # 成本趋势（显示人民币）
            cost_cny = [self._convert_usd_to_cny(c) for c in cost_usage]
            ax2.plot(hours, cost_cny, marker='s', linewidth=2, color='#FF6B6B')
            ax2.set_title(self.LABELS['charts']['cost_trend'])
            ax2.set_ylabel('成本 (￥)')
            ax2.set_xlabel('时间 (小时)')
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(output_path / 'enhanced_trends.png', dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            print(f"创建趋势图表失败: {e}")

    def _create_enhanced_efficiency_charts(self, output_path: Path):
        """创建增强版效率图表"""
        # 简化版效率图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle(self.LABELS['title']['efficiency'], fontsize=16, fontweight='bold')
        
        # 模拟效率数据
        operations = ['规划\n(Planning)', '写作\n(Writing)', '审查\n(Examining)']
        speeds = [150, 120, 180]  # tokens/sec
        response_times = [2.5, 3.2, 1.8]  # seconds
        
        # Token处理速度
        bars1 = ax1.barh(operations, speeds, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
        ax1.set_title(self.LABELS['charts']['processing_speed'])
        ax1.set_xlabel(self.LABELS['units']['tokens_per_sec'])
        
        # 添加数值标签
        for i, bar in enumerate(bars1):
            width = bar.get_width()
            ax1.text(width, bar.get_y() + bar.get_height()/2,
                    f'{speeds[i]}', ha='left', va='center')
        
        # 平均响应时间
        bars2 = ax2.barh(operations, response_times, color=['#FFD93D', '#6BCF7F', '#4D96FF'])
        ax2.set_title(self.LABELS['charts']['response_time'])
        ax2.set_xlabel(self.LABELS['units']['seconds'])
        
        # 添加数值标签
        for i, bar in enumerate(bars2):
            width = bar.get_width()
            ax2.text(width, bar.get_y() + bar.get_height()/2,
                    f'{response_times[i]}s', ha='left', va='center')
        
        plt.tight_layout()
        plt.savefig(output_path / 'enhanced_efficiency.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _create_enhanced_cost_charts(self, output_path: Path):
        """创建增强版成本图表"""
        try:
            session_stats = self.get_usage_data()
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            fig.suptitle(self.LABELS['title']['cost'], fontsize=16, fontweight='bold')
            
            # 成本分布（人民币）
            operations = session_stats.get('operations', {})
            if operations:
                total_cost = session_stats.get('total_cost', 0)
                costs_usd = [total_cost * (count / sum(operations.values())) for count in operations.values()]
                costs_cny = [self._convert_usd_to_cny(cost) for cost in costs_usd]
                
                # 翻译操作名称
                translated_ops = []
                for op in operations.keys():
                    if op == 'planning':
                        translated_ops.append('规划\n(Planning)')
                    elif op == 'writing':
                        translated_ops.append('写作\n(Writing)')
                    elif op == 'examining':
                        translated_ops.append('审查\n(Examining)')
                    else:
                        translated_ops.append(op)
                
                # 饼图
                ax1.pie(costs_cny, labels=translated_ops, autopct='%1.1f%%')
                ax1.set_title(self.LABELS['charts']['cost_distribution'])
                
                # 条形图
                bars = ax2.bar(translated_ops, costs_cny, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
                ax2.set_title('成本详情 (Cost Details)')
                ax2.set_ylabel('成本 (￥)')
                ax2.tick_params(axis='x', rotation=45)
                
                # 添加数值标签
                for i, bar in enumerate(bars):
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height,
                            f'￥{height:.4f}', ha='center', va='bottom')
            
            plt.tight_layout()
            plt.savefig(output_path / 'enhanced_costs.png', dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            print(f"创建成本图表失败: {e}")

    def _generate_enhanced_html_report(self, html_path: Path, charts_path: Path):
        """生成增强版HTML报告"""
        try:
            session_stats = self.get_usage_data()
        except ValueError:
            session_stats = {
                'session_id': 'unknown',
                'total_tokens': 0,
                'total_cost': 0,
                'api_calls': 0,
                'success_rate': 0
            }

        # 计算人民币成本
        total_cost_usd = session_stats.get('total_cost', 0)
        total_cost_cny = self._convert_usd_to_cny(total_cost_usd)

        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AutoPatent Token使用报告 (Enhanced Report)</title>
            <style>
                body {{ 
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif; 
                    margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                }}
                .container {{ 
                    max-width: 1400px; margin: 0 auto; background: white; 
                    padding: 40px; border-radius: 15px; 
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                }}
                .header {{ 
                    text-align: center; margin-bottom: 40px; 
                    padding-bottom: 30px; border-bottom: 3px solid #667eea; 
                }}
                .title {{ 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                    font-size: 2.8em; margin-bottom: 10px; font-weight: bold;
                }}
                .subtitle {{ color: #666; font-size: 1.3em; }}
                .stats-grid {{ 
                    display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
                    gap: 25px; margin: 30px 0; 
                }}
                .stat-card {{ 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                    color: white; padding: 25px; border-radius: 15px; 
                    text-align: center; box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                    transition: transform 0.3s ease;
                }}
                .stat-card:hover {{ transform: translateY(-5px); }}
                .stat-value {{ font-size: 2.2em; font-weight: bold; margin-bottom: 8px; }}
                .stat-label {{ font-size: 1em; opacity: 0.9; }}
                .section {{ margin: 40px 0; }}
                .section-title {{ 
                    color: #2c3e50; font-size: 1.8em; margin-bottom: 20px; 
                    padding-bottom: 15px; border-bottom: 2px solid #ecf0f1; 
                    font-weight: bold;
                }}
                .chart {{ 
                    text-align: center; margin: 25px 0; 
                    background: #f8f9fa; padding: 20px; border-radius: 10px;
                }}
                .chart img {{ 
                    max-width: 100%; height: auto; border-radius: 10px; 
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1); 
                }}
                .footer {{ 
                    text-align: center; margin-top: 50px; 
                    padding-top: 30px; border-top: 2px solid #e0e0e0; 
                    color: #7f8c8d; 
                }}
                .currency-note {{
                    background: #fff3cd; border: 1px solid #ffeaa7; 
                    padding: 15px; border-radius: 8px; margin: 20px 0;
                    color: #856404;
                }}
                .highlight {{ color: #e74c3c; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 class="title">AutoPatent Token使用报告</h1>
                    <h2 class="title" style="font-size: 1.5em;">Enhanced Token Usage Report</h2>
                    <p class="subtitle">Session ID: {session_stats.get('session_id', 'unknown')}</p>
                    <p class="subtitle">生成时间 (Generated): {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>

                <div class="currency-note">
                    <strong>💰 计费说明 (Billing Information):</strong><br>
                    • 原始成本以美元计算，汇率按 1 USD = {self.USD_TO_CNY_RATE} CNY 换算<br>
                    • DeepSeek API 实际计费以人民币为准<br>
                    • Original costs in USD, converted at rate 1 USD = {self.USD_TO_CNY_RATE} CNY
                </div>

                <div class="section">
                    <h2 class="section-title">📊 会话概览 (Session Overview)</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">{session_stats.get('total_tokens', 0):,}</div>
                            <div class="stat-label">{self.LABELS['metrics']['total_tokens']}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${total_cost_usd:.4f}</div>
                            <div class="stat-label">{self.LABELS['metrics']['total_cost_usd']}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value highlight">￥{total_cost_cny:.4f}</div>
                            <div class="stat-label">{self.LABELS['metrics']['total_cost_cny']}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{session_stats.get('api_calls', 0)}</div>
                            <div class="stat-label">{self.LABELS['metrics']['api_calls']}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{session_stats.get('success_rate', 0):.1f}%</div>
                            <div class="stat-label">{self.LABELS['metrics']['success_rate']}</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">📈 {self.LABELS['title']['overview']}</h2>
                    <div class="chart">
                        <img src="enhanced_overview.png" alt="Enhanced Overview Chart">
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">📊 {self.LABELS['title']['trends']}</h2>
                    <div class="chart">
                        <img src="enhanced_trends.png" alt="Enhanced Trends Chart">
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">⚡ {self.LABELS['title']['efficiency']}</h2>
                    <div class="chart">
                        <img src="enhanced_efficiency.png" alt="Enhanced Efficiency Chart">
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">💰 {self.LABELS['title']['cost']}</h2>
                    <div class="chart">
                        <img src="enhanced_costs.png" alt="Enhanced Cost Chart">
                    </div>
                </div>

                <div class="footer">
                    <p><strong>AutoPatent Enhanced Token Tracker</strong></p>
                    <p>增强版Token追踪器 - 支持中英混合界面和人民币计费</p>
                    <p>Enhanced Token Tracker with bilingual UI and CNY billing support</p>
                </div>
            </div>
        </body>
        </html>
        """

        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"✅ 增强版HTML报告已生成: {html_path}")


if __name__ == "__main__":
    # 演示用法
    print("🎨 增强版Token可视化器演示")
    
    # 创建可视化器
    visualizer = EnhancedTokenVisualizer()
    
    # 模拟一些数据
    visualizer._session_data = {
        "session统计": {
            "session_id": "demo_session",
            "total_tokens": 5000,
            "total_input_tokens": 2000,
            "total_output_tokens": 2500,
            "total_reasoning_tokens": 500,
            "total_cost": 0.05,  # USD
            "api_calls": 15,
            "success_rate": 95.0,
            "operations": {"planning": 5, "writing": 8, "examining": 2},
            "models_used": {"deepseek-chat": 12, "deepseek-reasoner": 3},
            "agents_used": {"PlannerAgent": 5, "WriterAgent": 8, "ExaminerAgent": 2}
        }
    }
    
    # 生成报告
    report_path = visualizer.create_enhanced_dashboard("demo_reports")
    print(f"📊 演示报告已生成: {report_path}")
