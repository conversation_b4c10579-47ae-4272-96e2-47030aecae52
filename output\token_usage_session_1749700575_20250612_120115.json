{
  "导出信息": {
    "导出时间": "2025-06-12T12:01:15.020772",
    "session_id": "session_1749700575",
    "记录数量": 2,
    "格式版本": "1.0"
  },
  "session统计": {
    "session_id": "session_1749700575",
    "start_time": "2025-06-12 11:56:15.014632",
    "total_input_tokens": 745,
    "total_output_tokens": 390,
    "total_reasoning_tokens": 0,
    "total_cost": 0.000213,
    "api_calls": 1,
    "successful_calls": 1,
    "failed_calls": 0,
    "operations": {
      "content_generation": 1
    },
    "models_used": {
      "deepseek-chat": 1
    },
    "agents_used": {
      "LLMClient": 1
    },
    "duration_seconds": 300.00614,
    "duration_formatted": "0:05:00",
    "total_tokens": 1135,
    "average_response_time": 26.644344329833984,
    "success_rate": 100.0
  },
  "详细分析": {
    "总记录数": 2,
    "时间范围": {
      "开始时间": "2025-06-12T11:56:43.064559",
      "结束时间": "2025-06-12T11:56:43.064559"
    },
    "token统计": {
      "input": 745,
      "output": 390
    },
    "成本统计": {
      "input": 0.000104,
      "output": 0.000109
    },
    "总成本": 0.000213,
    "操作统计": {
      "content_generation": {
        "次数": 2,
        "tokens": 1135,
        "成本": 0.000213
      }
    },
    "智能体统计": {
      "LLMClient": {
        "次数": 2,
        "tokens": 1135,
        "成本": 0.000213
      }
    },
    "模型统计": {
      "deepseek-chat": {
        "次数": 2,
        "tokens": 1135,
        "成本": 0.000213
      }
    },
    "性能统计": {
      "成功率": 100.0,
      "平均响应时间": 26.644344329833984,
      "最快响应": 26.644344329833984,
      "最慢响应": 26.644344329833984,
      "失败次数": 0
    },
    "时间趋势": {
      "2025-06-12 11:00": {
        "tokens": 1135,
        "成本": 0.000213,
        "调用次数": 2
      }
    }
  },
  "成本估算": {
    "input_cost": 0.000104,
    "output_cost": 0.000109,
    "reasoning_cost": 0.0,
    "total_cost": 0.000213,
    "input_rate": 0.00014,
    "output_rate": 0.00028,
    "currency": "USD"
  },
  "原始记录": [
    {
      "timestamp": "2025-06-12T11:56:43.064559",
      "operation": "content_generation",
      "agent_name": "LLMClient",
      "token_type": "input",
      "count": 745,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000104,
      "response_time": 26.644344329833984,
      "session_id": "session_1749700575",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:56:43.064559",
      "operation": "content_generation",
      "agent_name": "LLMClient",
      "token_type": "output",
      "count": 390,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000109,
      "response_time": 26.644344329833984,
      "session_id": "session_1749700575",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    }
  ]
}ing",
      "agent_name": "PlannerAgent",
      "token_type": "input",
      "count": 834,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000117,
      "response_time": 28.056927,
      "session_id": "session_1749700575",
      "workflow_id": "smart_mining_metaverse_fixed_1749700575",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 6.4,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:56:43.066558",
      "operation": "planning",
      "agent_name": "PlannerAgent",
      "token_type": "output",
      "count": 2794,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000782,
      "response_time": 28.056927,
      "session_id": "session_1749700575",
      "workflow_id": "smart_mining_metaverse_fixed_1749700575",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 6.4,
      "success": true,
      "error_message": ""
    }
  ]
}