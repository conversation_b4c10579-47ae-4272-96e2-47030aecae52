#!/usr/bin/env python3
"""
AutoPatent简化演示版本 - 不依赖外部API的演示
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class SimpleTokenTracker:
    """简化的Token追踪器"""
    def __init__(self):
        self.total_tokens = 0
        self.operations = []
    
    def add_tokens(self, operation, agent_name, input_tokens, output_tokens, **kwargs):
        self.total_tokens += input_tokens + output_tokens
        self.operations.append({
            'operation': operation,
            'agent_name': agent_name,
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'timestamp': datetime.now().isoformat()
        })
    
    def get_usage(self):
        return {
            'total_tokens': self.total_tokens,
            'operations_count': len(self.operations)
        }

class SimpleAgent:
    """简化的智能体基类"""
    def __init__(self, agent_name: str, agent_type: str):
        self.agent_name = agent_name
        self.agent_type = agent_type
        self.token_tracker = SimpleTokenTracker()
        self.logger = logging.getLogger(f"AutoPatent.{agent_name}")
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据"""
        self.logger.info(f"{self.agent_name} 开始处理...")
        
        # 模拟处理时间
        import time
        time.sleep(0.5)
        
        # 模拟token使用
        self.token_tracker.add_tokens(
            operation=self.agent_type,
            agent_name=self.agent_name,
            input_tokens=100,
            output_tokens=200
        )
        
        return self._generate_mock_result(input_data)
    
    def _generate_mock_result(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成模拟结果"""
        return {
            'success': True,
            'agent': self.agent_name,
            'processed_at': datetime.now().isoformat()
        }

class SimplePlannerAgent(SimpleAgent):
    """简化的规划智能体"""
    def __init__(self):
        super().__init__("PlannerAgent", "planning")
    
    def _generate_mock_result(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        concept = input_data.get('concept', '')
        
        # 模拟生成PGTree结构
        pgtree = {
            'structure': {
                'abstract': {'status': 'planned', 'priority': 1},
                'background': {'status': 'planned', 'priority': 2},
                'summary': {'status': 'planned', 'priority': 3},
                'detailed_description': {'status': 'planned', 'priority': 4},
                'claims': {'status': 'planned', 'priority': 5}
            },
            'concept': concept[:100] + "..." if len(concept) > 100 else concept
        }
        
        return {
            'success': True,
            'pgtree': pgtree,
            'plan_details': {
                'sections_planned': 5,
                'estimated_time': '30分钟',
                'complexity_score': 7.5
            }
        }

class SimpleWriterAgent(SimpleAgent):
    """简化的写作智能体"""
    def __init__(self, writer_type: str = 'general'):
        super().__init__(f"WriterAgent-{writer_type}", "writing")
        self.writer_type = writer_type
    
    def _generate_mock_result(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        section = input_data.get('section', 'unknown')
        
        # 模拟生成内容
        mock_content = self._generate_mock_content(section)
        
        # 更新PGTree
        pgtree = input_data.get('pgtree', {})
        if 'structure' in pgtree and section in pgtree['structure']:
            pgtree['structure'][section]['content'] = mock_content
            pgtree['structure'][section]['status'] = 'completed'
        
        return {
            'success': True,
            'content': mock_content,
            'pgtree': pgtree,
            'word_count': len(mock_content.split()),
            'section': section
        }
    
    def _generate_mock_content(self, section: str) -> str:
        """生成模拟内容"""
        content_templates = {
            'abstract': "本发明提供了一种创新的技术解决方案，通过先进的算法和系统设计，实现了显著的性能提升和用户体验改善。该技术具有广泛的应用前景和商业价值。",
            'background': "随着技术的快速发展，现有解决方案面临诸多挑战和限制。本发明针对这些问题，提出了一种全新的技术路径，能够有效解决现有技术的不足。",
            'summary': "本发明的核心创新在于采用了独特的技术架构和算法设计，通过多个技术模块的协同工作，实现了系统性能的大幅提升。",
            'detailed_description': "本发明的详细实施方案包括多个关键技术组件：1）核心算法模块；2）数据处理单元；3）用户交互界面；4）系统集成框架。各组件通过标准化接口实现无缝集成。",
            'claims': "1. 一种创新的技术系统，其特征在于包括：核心处理模块、数据管理单元和用户交互界面。\n2. 根据权利要求1所述的系统，其特征在于所述核心处理模块采用先进的算法架构。"
        }
        
        return content_templates.get(section, f"这是{section}部分的模拟内容，展示了相关的技术特征和创新点。")

class SimpleExaminerAgent(SimpleAgent):
    """简化的审查智能体"""
    def __init__(self):
        super().__init__("ExaminerAgent", "examining")
    
    def _generate_mock_result(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        pgtree = input_data.get('pgtree', {})
        
        # 模拟审查评分
        sections_scores = {}
        overall_scores = []
        
        if 'structure' in pgtree:
            for section, data in pgtree['structure'].items():
                if data.get('content'):
                    # 模拟评分（6-9分）
                    score = 7.0 + (hash(section) % 20) / 10
                    sections_scores[section] = score
                    overall_scores.append(score)
        
        overall_score = sum(overall_scores) / len(overall_scores) if overall_scores else 6.0
        
        # 生成模拟建议
        recommendations = []
        if overall_score < 7.5:
            recommendations.append("建议增强技术描述的详细程度")
            recommendations.append("建议补充更多的技术优势说明")
        
        return {
            'success': True,
            'overall_score': overall_score,
            'section_scores': sections_scores,
            'recommendations': recommendations,
            'examination_details': {
                'sections_examined': len(sections_scores),
                'quality_level': 'good' if overall_score >= 7.0 else 'needs_improvement'
            }
        }

class SimpleCoordinator:
    """简化的协调器"""
    def __init__(self):
        self.planner_agent = SimplePlannerAgent()
        self.writer_agents = {
            'general': SimpleWriterAgent('general'),
            'technical': SimpleWriterAgent('technical'),
            'legal': SimpleWriterAgent('legal')
        }
        self.examiner_agent = SimpleExaminerAgent()
        self.logger = logging.getLogger("AutoPatent.SimpleCoordinator")
    
    def generate_patent(self, concept: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成专利的主流程"""
        if not options:
            options = {}
        
        self.logger.info("开始专利生成流程...")
        
        try:
            # 1. 规划阶段
            self.logger.info("执行规划阶段...")
            planning_result = self.planner_agent.process({
                'concept': concept,
                'options': options
            })
            
            if not planning_result['success']:
                return {'success': False, 'error': '规划阶段失败'}
            
            pgtree = planning_result['pgtree']
            
            # 2. 写作阶段
            self.logger.info("执行写作阶段...")
            writing_order = ['background', 'summary', 'detailed_description', 'claims', 'abstract']
            
            for section in writing_order:
                if section in pgtree['structure']:
                    self.logger.info(f"写作section: {section}")
                    writer_agent = self._select_writer_agent(section)
                    
                    writing_result = writer_agent.process({
                        'pgtree': pgtree,
                        'section': section,
                        'context': {'concept': concept}
                    })
                    
                    if writing_result['success']:
                        pgtree = writing_result['pgtree']
            
            # 3. 审查阶段
            self.logger.info("执行审查阶段...")
            examination_result = self.examiner_agent.process({
                'pgtree': pgtree
            })
            
            # 生成最终结果
            patent_content = {}
            for section, data in pgtree['structure'].items():
                if data.get('content'):
                    patent_content[section] = data['content']
            
            return {
                'success': True,
                'patent_content': patent_content,
                'workflow_state': 'completed',
                'statistics': {
                    'iterations': 1,
                    'completion_time': datetime.now().isoformat(),
                    'sections_generated': len(patent_content)
                },
                'quality_score': examination_result.get('overall_score', 7.0),
                'token_usage': self._collect_token_usage()
            }
            
        except Exception as e:
            self.logger.error(f"专利生成失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _select_writer_agent(self, section: str):
        """选择合适的写作智能体"""
        if section == 'claims':
            return self.writer_agents['legal']
        elif section in ['detailed_description', 'background']:
            return self.writer_agents['technical']
        else:
            return self.writer_agents['general']
    
    def _collect_token_usage(self):
        """收集token使用统计"""
        total_tokens = 0
        total_tokens += self.planner_agent.token_tracker.total_tokens
        total_tokens += self.examiner_agent.token_tracker.total_tokens
        
        for agent in self.writer_agents.values():
            total_tokens += agent.token_tracker.total_tokens
        
        return {
            'total': {'total_tokens': total_tokens},
            'by_agent': {
                'planner': self.planner_agent.token_tracker.get_usage(),
                'examiner': self.examiner_agent.token_tracker.get_usage(),
                'writers': {name: agent.token_tracker.get_usage() 
                           for name, agent in self.writer_agents.items()}
            }
        }

def main():
    """主演示函数"""
    print("🚀 AutoPatent简化演示版本")
    print("=" * 60)
    
    # 示例概念
    concept = """
    一种基于人工智能的智能推荐系统，该系统通过深度学习算法分析用户行为模式，
    实现个性化内容推荐。系统具有自适应学习能力，能够根据用户反馈持续优化推荐效果。
    
    主要创新点：
    1. 多模态数据融合技术
    2. 实时学习算法
    3. 隐私保护机制
    4. 可解释性推荐框架
    """
    
    options = {
        'technical_field': '人工智能',
        'patent_type': 'invention'
    }
    
    print(f"📝 专利概念: {concept[:100]}...")
    print(f"🔬 技术领域: {options['technical_field']}")
    
    # 创建协调器并生成专利
    coordinator = SimpleCoordinator()
    
    print("\n🔄 开始专利生成流程...")
    result = coordinator.generate_patent(concept, options)
    
    # 显示结果
    if result['success']:
        print("\n✅ 专利生成成功！")
        print(f"📊 质量评分: {result['quality_score']:.1f}/10")
        print(f"📄 生成章节数: {result['statistics']['sections_generated']}")
        
        print("\n📋 生成的专利内容:")
        print("-" * 40)
        
        for section, content in result['patent_content'].items():
            print(f"\n【{section.upper()}】")
            preview = content[:150] + "..." if len(content) > 150 else content
            print(preview)
        
        # 保存结果
        output_file = f"simple_patent_demo_{int(datetime.now().timestamp())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 完整结果已保存到: {output_file}")
        
    else:
        print(f"\n❌ 专利生成失败: {result['error']}")
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("\n💡 这是一个简化的演示版本，展示了AutoPatent的基本工作流程。")
    print("完整版本需要配置API密钥并安装所有依赖包。")

if __name__ == "__main__":
    main()
