#!/usr/bin/env python3
"""
项目结构重组脚本

将混乱的项目结构重新组织为标准的Python包结构
"""

import os
import shutil
from pathlib import Path
import json


def create_proper_structure():
    """创建正确的项目结构"""

    print("🔧 开始重组项目结构...")

    # 定义正确的项目结构
    proper_structure = {
        "autopatent/": {
            "description": "主要的Python包",
            "subdirs": ["agents/", "coordinator/", "database/", "utils/", "cli/"],
        },
        "examples/": {"description": "使用示例和演示脚本", "subdirs": []},
        "tests/": {"description": "测试文件", "subdirs": []},
        "docs/": {"description": "文档", "subdirs": []},
        "scripts/": {"description": "辅助脚本", "subdirs": []},
        "output/": {
            "description": "输出文件",
            "subdirs": ["patents/", "reports/", "logs/", "databases/"],
        },
        "config/": {"description": "配置文件", "subdirs": []},
    }

    # 1. 备份当前状态
    backup_current_state()

    # 2. 清理重复的根目录文件
    cleanup_root_duplicates()

    # 3. 移动文件到正确位置
    reorganize_files()

    # 4. 更新导入路径
    update_imports()

    # 5. 创建新的入口文件
    create_entry_points()

    print("✅ 项目结构重组完成！")
    print_new_structure()


def backup_current_state():
    """备份当前状态"""
    print("📦 备份当前状态...")

    backup_dir = Path("backup_before_reorganize")
    if backup_dir.exists():
        shutil.rmtree(backup_dir)

    # 备份重要文件
    important_files = [
        "smart_mining_metaverse_patent_fixed.py",
        "smart_mining_metaverse_patent_fixed_20250612_115612.json",
        "llm_mode_demo.py",
        "config.py",
    ]

    backup_dir.mkdir()
    for file in important_files:
        if Path(file).exists():
            shutil.copy2(file, backup_dir / file)

    print(f"✅ 重要文件已备份到 {backup_dir}")


def cleanup_root_duplicates():
    """清理根目录的重复文件"""
    print("🧹 清理根目录重复文件...")

    # 要删除的根目录重复文件夹
    duplicate_dirs = ["agents", "coordinator", "database", "utils"]

    for dir_name in duplicate_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists() and dir_path.is_dir():
            print(f"  删除重复目录: {dir_name}/")
            shutil.rmtree(dir_path)

    # 清理临时文件
    temp_patterns = [
        "*.db",
        "token_usage_session_*.json",
        "patent_demo_*.json",
        "result.json",
        "quick_example.db",
    ]

    for pattern in temp_patterns:
        for file in Path(".").glob(pattern):
            print(f"  移动临时文件: {file} -> output/")
            ensure_dir("output")
            shutil.move(str(file), f"output/{file.name}")


def reorganize_files():
    """重新组织文件"""
    print("📁 重新组织文件...")

    # 移动示例文件
    if Path("examples").exists():
        print("  保持 examples/ 目录")

    # 移动脚本文件
    script_files = [
        "smart_mining_metaverse_patent_fixed.py",
        "smart_mining_metaverse_improved.py",
        "smart_mining_metaverse_simple.py",
        "llm_mode_demo.py",
        "run_autopatent.py",
    ]

    ensure_dir("examples/patent_generation")
    for script in script_files:
        if Path(script).exists():
            print(f"  移动脚本: {script} -> examples/patent_generation/")
            shutil.move(script, f"examples/patent_generation/{script}")

    # 移动输出文件
    output_files = list(Path(".").glob("smart_mining_metaverse_*.json"))
    ensure_dir("output/patents")
    for file in output_files:
        print(f"  移动输出文件: {file} -> output/patents/")
        shutil.move(str(file), f"output/patents/{file.name}")

    # 移动配置文件
    if Path("config.py").exists():
        ensure_dir("config")
        print("  移动配置文件: config.py -> config/")
        shutil.move("config.py", "config/config.py")

    # 移动日志目录
    if Path("logs").exists():
        if Path("output/logs").exists():
            shutil.rmtree("output/logs")
        print("  移动日志目录: logs/ -> output/logs/")
        shutil.move("logs", "output/logs")


def update_imports():
    """更新导入路径"""
    print("🔄 更新导入路径...")

    # 更新autopatent包中的导入
    autopatent_files = list(Path("autopatent").rglob("*.py"))

    for file_path in autopatent_files:
        if file_path.name == "__init__.py":
            continue

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 修复常见的导入问题
            old_imports = [
                "from config import",
                "import config",
                "from utils.",
                "from database.",
                "from agents.",
                "from coordinator.",
            ]

            new_imports = [
                "from config.config import",
                "import config.config as config",
                "from autopatent.utils.",
                "from autopatent.database.",
                "from autopatent.agents.",
                "from autopatent.coordinator.",
            ]

            modified = False
            for old, new in zip(old_imports, new_imports):
                if old in content:
                    content = content.replace(old, new)
                    modified = True

            if modified:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                print(f"  更新导入: {file_path}")

        except Exception as e:
            print(f"  警告: 无法更新 {file_path}: {e}")


def create_entry_points():
    """创建新的入口文件"""
    print("🚀 创建入口文件...")

    # 创建主入口文件
    main_entry = """#!/usr/bin/env python3
\"\"\"
AutoPatent - 智能专利生成系统

主入口文件
\"\"\"

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from autopatent.cli.cli import main as cli_main


def main():
    \"\"\"主函数\"\"\"
    print("🏔️  AutoPatent - 智能专利生成系统")
    print("=" * 50)
    
    try:
        cli_main()
    except KeyboardInterrupt:
        print("\\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
"""

    with open("main.py", "w", encoding="utf-8") as f:
        f.write(main_entry)

    # 创建简化的运行脚本
    run_script = """#!/usr/bin/env python3
\"\"\"
快速运行脚本
\"\"\"

import subprocess
import sys

def main():
    \"\"\"运行AutoPatent\"\"\"
    try:
        subprocess.run([sys.executable, "main.py"] + sys.argv[1:])
    except KeyboardInterrupt:
        print("\\n👋 用户中断")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

if __name__ == "__main__":
    main()
"""

    with open("run.py", "w", encoding="utf-8") as f:
        f.write(run_script)

    print("✅ 创建了新的入口文件: main.py, run.py")


def ensure_dir(path):
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)


def print_new_structure():
    """打印新的项目结构"""
    print("\n📋 新的项目结构:")
    print("=" * 50)

    structure = """
draftPatents/                    # 项目根目录
├── main.py                      # 主入口文件
├── run.py                       # 快速运行脚本
├── README.md                    # 项目说明
├── requirements.txt             # 依赖列表
├── .env.template               # 环境变量模板
│
├── autopatent/                  # 主要Python包
│   ├── __init__.py
│   ├── agents/                  # 智能体模块
│   │   ├── __init__.py
│   │   ├── base_agent.py
│   │   ├── planner_agent.py
│   │   ├── writer_agent.py
│   │   └── examiner_agent.py
│   ├── coordinator/             # 协调器模块
│   │   ├── __init__.py
│   │   ├── coordinator.py
│   │   └── workflow_manager.py
│   ├── database/                # 数据库模块
│   │   ├── __init__.py
│   │   └── patent_db.py
│   ├── utils/                   # 工具模块
│   │   ├── __init__.py
│   │   ├── llm_client.py
│   │   ├── pg_tree_handler.py
│   │   ├── token_tracker.py
│   │   └── token_visualizer.py
│   └── cli/                     # 命令行界面
│       ├── __init__.py
│       └── cli.py
│
├── examples/                    # 使用示例
│   ├── README.md
│   ├── basic_usage/            # 基础使用示例
│   ├── agent_examples/         # 智能体示例
│   └── patent_generation/      # 专利生成示例
│
├── tests/                       # 测试文件
│   ├── __init__.py
│   ├── test_agents/
│   ├── test_utils/
│   └── test_integration/
│
├── docs/                        # 文档
│   ├── README.md
│   ├── API.md
│   ├── TUTORIAL.md
│   └── CHANGELOG.md
│
├── scripts/                     # 辅助脚本
│   ├── install_dependencies.py
│   ├── setup_environment.py
│   └── run_tests.py
│
├── config/                      # 配置文件
│   ├── config.py
│   ├── logging.yaml
│   └── models.yaml
│
└── output/                      # 输出目录
    ├── patents/                 # 生成的专利
    ├── reports/                 # 报告文件
    ├── logs/                    # 日志文件
    └── databases/               # 数据库文件
    """

    print(structure)

    print("\n💡 使用方法:")
    print("  python main.py          # 启动主程序")
    print("  python run.py           # 快速运行")
    print("  python -m autopatent    # 作为模块运行")


if __name__ == "__main__":
    create_proper_structure()
