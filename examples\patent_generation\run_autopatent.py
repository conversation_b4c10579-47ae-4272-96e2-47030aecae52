"""
AutoPatent启动脚本
"""

import os
import sys
import argparse
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def check_environment():
    """检查环境配置"""
    print("检查环境配置...")

    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误：需要Python 3.8或更高版本")
        return False

    # 检查必要的环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("警告：未设置DEEPSEEK_API_KEY环境变量")
        print("请在.env文件中设置或通过环境变量设置")
        return False

    # 检查必要的包
    required_packages = [
        "langchain_openai",
        "pydantic",
        "python-dotenv",
        "numpy",
        "sqlite3",
    ]

    missing_packages = []
    for package in required_packages:
        try:
            if package == "sqlite3":
                import sqlite3
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"错误：缺少必要的包: {', '.join(missing_packages)}")
        print("请运行: pip install langchain-openai pydantic python-dotenv numpy")
        return False

    print("✓ 环境检查通过")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AutoPatent - 多智能体专利生成系统")
    parser.add_argument("--concept", "-c", type=str, help="专利概念描述")
    parser.add_argument("--field", "-f", type=str, default="通用技术", help="技术领域")
    parser.add_argument("--output", "-o", type=str, help="输出文件路径")
    parser.add_argument("--demo", action="store_true", help="运行演示模式")
    parser.add_argument("--interactive", "-i", action="store_true", help="交互式模式")

    args = parser.parse_args()

    # 检查环境
    if not check_environment():
        sys.exit(1)

    if args.demo:
        # 运行演示
        print("启动演示模式...")
        from examples.example_usage import main as demo_main

        demo_main()

    elif args.interactive:
        # 交互式模式
        interactive_mode()

    elif args.concept:
        # 命令行模式
        generate_patent_cli(args.concept, args.field, args.output)

    else:
        # 显示帮助
        parser.print_help()


def interactive_mode():
    """交互式模式"""
    print("\n" + "=" * 50)
    print("AutoPatent 交互式模式")
    print("=" * 50)

    # 获取用户输入
    print("\n请输入您的发明概念：")
    concept = input("> ")

    if not concept.strip():
        print("错误：未输入发明概念")
        return

    print("\n请输入技术领域（可选，回车使用默认）：")
    field = input("> ") or "通用技术领域"

    print("\n请选择专利类型：")
    print("1. 发明专利（默认）")
    print("2. 实用新型专利")
    print("3. 外观设计专利")
    choice = input("> ") or "1"

    patent_types = {"1": "invention", "2": "utility_model", "3": "design"}
    patent_type = patent_types.get(choice, "invention")

    # 构建选项
    options = {
        "technical_field": field,
        "target_length": "standard",
        "patent_type": patent_type,
        "claim_count": {"independent": 2, "dependent": 8},
    }

    # 生成专利
    generate_patent_interactive(concept, options)


def generate_patent_cli(concept: str, field: str, output_path: Optional[str] = None):
    """命令行模式生成专利"""
    from coordinator.coordinator import AutoPatentCoordinator

    print(f"\n正在生成专利...")
    print(f"概念：{concept[:100]}...")
    print(f"技术领域：{field}")

    coordinator = AutoPatentCoordinator()
    options = {"technical_field": field, "target_length": "standard"}

    result = coordinator.generate_patent(concept, options)

    if result["success"]:
        print("✓ 专利生成成功")

        # 保存结果
        if output_path:
            import json

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            print(f"✓ 结果已保存到：{output_path}")
        else:
            # 显示简要结果
            for section, content in result["patent_content"].items():
                print(f"\n【{section.upper()}】")
                print(content[:200] + "...")
    else:
        print(f"✗ 专利生成失败：{result['error']}")


def generate_patent_interactive(concept: str, options: Dict[str, Any]):
    """交互式生成专利"""
    from coordinator.coordinator import AutoPatentCoordinator
    import json
    import time

    print(f"\n开始生成专利...")
    print(f"概念：{concept[:100]}...")
    print(f"选项：{options}")

    coordinator = AutoPatentCoordinator()

    # 显示进度
    print("\n进度：")
    print("□ 规划阶段")
    print("□ 撰写阶段")
    print("□ 审查阶段")
    print("□ 完成")

    result = coordinator.generate_patent(concept, options)

    if result["success"]:
        print("\n✓ 专利生成完成！")

        # 询问是否保存
        save_choice = input("\n是否保存结果到文件？(y/N): ")
        if save_choice.lower() == "y":
            filename = f"patent_{int(time.time())}.json"
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            print(f"✓ 结果已保存到：{filename}")

        # 显示简要内容
        print("\n生成的专利内容：")
        for section, content in result["patent_content"].items():
            print(f"\n【{section.upper()}】")
            print(content[:300] + "..." if len(content) > 300 else content)

    else:
        print(f"\n✗ 专利生成失败：{result['error']}")


if __name__ == "__main__":
    main()
