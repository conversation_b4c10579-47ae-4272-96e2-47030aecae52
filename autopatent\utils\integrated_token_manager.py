"""
集成Token管理器 - 将Token追踪和可视化功能完美融入AutoPatent项目
"""

import os
import logging
from typing import Dict, Any, Optional, Union
from datetime import datetime
from pathlib import Path

try:
    from .token_tracker import TokenTracker, OperationType
    from .enhanced_token_visualizer import EnhancedTokenVisualizer
except ImportError:
    # 兼容性导入
    try:
        from token_tracker import TokenTracker, OperationType
        from enhanced_token_visualizer import EnhancedTokenVisualizer
    except ImportError:
        print("⚠️  无法导入Token相关模块")
        TokenTracker = None
        OperationType = None
        EnhancedTokenVisualizer = None


class IntegratedTokenManager:
    """集成Token管理器 - AutoPatent专用"""
    
    def __init__(self, 
                 session_id: Optional[str] = None,
                 project_name: str = "AutoPatent",
                 enable_visualization: bool = True,
                 auto_save: bool = True,
                 output_dir: str = "output"):
        """
        初始化集成Token管理器
        
        Args:
            session_id: 会话ID
            project_name: 项目名称
            enable_visualization: 是否启用可视化
            auto_save: 是否自动保存
            output_dir: 输出目录
        """
        self.project_name = project_name
        self.enable_visualization = enable_visualization
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.reports_dir = self.output_dir / "reports"
        self.data_dir = self.output_dir / "token_data"
        self.reports_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        
        # 初始化Token追踪器
        if TokenTracker:
            self.tracker = TokenTracker(
                session_id=session_id or f"{project_name}_{int(datetime.now().timestamp())}",
                auto_save=auto_save
            )
        else:
            self.tracker = None
            print("⚠️  TokenTracker不可用，Token追踪功能将被禁用")
        
        # 初始化可视化器
        if EnhancedTokenVisualizer and self.enable_visualization:
            self.visualizer = EnhancedTokenVisualizer(self.tracker)
        else:
            self.visualizer = None
            if self.enable_visualization:
                print("⚠️  可视化功能不可用")
        
        # 日志记录器
        self.logger = logging.getLogger(f"AutoPatent.TokenManager")
        
        self.logger.info(f"集成Token管理器已初始化 - Session: {self.tracker.session_id if self.tracker else 'N/A'}")

    def track_agent_operation(self,
                            agent_name: str,
                            operation: Union[str, OperationType],
                            input_tokens: int = 0,
                            output_tokens: int = 0,
                            reasoning_tokens: int = 0,
                            model_name: str = "deepseek-chat",
                            response_time: float = 0.0,
                            success: bool = True,
                            error_message: str = "",
                            workflow_context: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        追踪智能体操作的Token使用
        
        Args:
            agent_name: 智能体名称
            operation: 操作类型
            input_tokens: 输入token数
            output_tokens: 输出token数
            reasoning_tokens: 推理token数
            model_name: 模型名称
            response_time: 响应时间
            success: 是否成功
            error_message: 错误信息
            workflow_context: 工作流上下文
            
        Returns:
            记录ID
        """
        if not self.tracker:
            return None
            
        # 提取工作流上下文信息
        workflow_id = ""
        node_id = ""
        if workflow_context:
            workflow_id = workflow_context.get('workflow_id', '')
            node_id = workflow_context.get('node_id', '')
        
        try:
            record_id = self.tracker.add_tokens(
                operation=operation,
                agent_name=agent_name,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                reasoning_tokens=reasoning_tokens,
                model_name=model_name,
                response_time=response_time,
                workflow_id=workflow_id,
                node_id=node_id,
                success=success,
                error_message=error_message
            )
            
            self.logger.debug(f"记录Token使用: {agent_name} - {operation} - {input_tokens + output_tokens + reasoning_tokens} tokens")
            return record_id
            
        except Exception as e:
            self.logger.error(f"记录Token使用失败: {e}")
            return None

    def get_session_summary(self) -> Dict[str, Any]:
        """获取当前会话摘要"""
        if not self.tracker:
            return {"error": "Token追踪器不可用"}
        
        try:
            usage_data = self.tracker.get_usage()
            
            # 添加人民币成本
            usd_cost = usage_data.get('total_cost', 0)
            cny_cost = usd_cost * 7.2  # 使用固定汇率
            
            summary = {
                "session_id": usage_data.get('session_id'),
                "project_name": self.project_name,
                "total_tokens": usage_data.get('total_tokens', 0),
                "total_cost_usd": usd_cost,
                "total_cost_cny": round(cny_cost, 4),
                "api_calls": usage_data.get('api_calls', 0),
                "success_rate": usage_data.get('success_rate', 0),
                "duration": usage_data.get('duration_formatted', '0:00:00'),
                "agents_used": usage_data.get('agents_used', {}),
                "operations": usage_data.get('operations', {}),
                "models_used": usage_data.get('models_used', {})
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"获取会话摘要失败: {e}")
            return {"error": str(e)}

    def generate_report(self, 
                       report_type: str = "enhanced",
                       include_charts: bool = True) -> Optional[str]:
        """
        生成Token使用报告
        
        Args:
            report_type: 报告类型 ("enhanced", "basic")
            include_charts: 是否包含图表
            
        Returns:
            报告文件路径
        """
        if not self.visualizer:
            self.logger.warning("可视化器不可用，无法生成报告")
            return None
        
        try:
            if report_type == "enhanced":
                report_path = self.visualizer.create_enhanced_dashboard(
                    str(self.reports_dir)
                )
            else:
                # 基础报告（如果需要的话）
                report_path = self.visualizer.create_enhanced_dashboard(
                    str(self.reports_dir)
                )
            
            self.logger.info(f"Token使用报告已生成: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            return None

    def export_data(self, 
                   format_type: str = "json",
                   filename: Optional[str] = None) -> Optional[str]:
        """
        导出Token使用数据
        
        Args:
            format_type: 导出格式 ("json", "csv", "xlsx")
            filename: 文件名（可选）
            
        Returns:
            导出文件路径
        """
        if not self.tracker:
            return None
        
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"token_usage_{self.project_name}_{timestamp}"
            
            export_path = self.tracker.export_usage(
                filepath=str(self.data_dir / filename),
                format_type=format_type
            )
            
            self.logger.info(f"Token数据已导出: {export_path}")
            return export_path
            
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            return None

    def get_cost_breakdown(self) -> Dict[str, Any]:
        """获取成本分解"""
        if not self.tracker:
            return {"error": "Token追踪器不可用"}
        
        try:
            detailed_usage = self.tracker.get_detailed_usage()
            
            # 按操作类型分解成本
            operation_costs = {}
            if "操作统计" in detailed_usage:
                for operation, stats in detailed_usage["操作统计"].items():
                    usd_cost = stats.get("成本", 0)
                    cny_cost = usd_cost * 7.2
                    
                    # 翻译操作名称
                    if operation == "planning":
                        op_name = "规划 (Planning)"
                    elif operation == "writing":
                        op_name = "写作 (Writing)"
                    elif operation == "examining":
                        op_name = "审查 (Examining)"
                    else:
                        op_name = operation
                    
                    operation_costs[op_name] = {
                        "tokens": stats.get("tokens", 0),
                        "calls": stats.get("次数", 0),
                        "cost_usd": round(usd_cost, 6),
                        "cost_cny": round(cny_cost, 4)
                    }
            
            # 按智能体分解成本
            agent_costs = {}
            if "智能体统计" in detailed_usage:
                for agent, stats in detailed_usage["智能体统计"].items():
                    usd_cost = stats.get("成本", 0)
                    cny_cost = usd_cost * 7.2
                    
                    agent_costs[agent] = {
                        "tokens": stats.get("tokens", 0),
                        "calls": stats.get("次数", 0),
                        "cost_usd": round(usd_cost, 6),
                        "cost_cny": round(cny_cost, 4)
                    }
            
            return {
                "by_operation": operation_costs,
                "by_agent": agent_costs,
                "total_cost_usd": detailed_usage.get("总成本", 0),
                "total_cost_cny": round(detailed_usage.get("总成本", 0) * 7.2, 4)
            }
            
        except Exception as e:
            self.logger.error(f"获取成本分解失败: {e}")
            return {"error": str(e)}

    def print_summary(self):
        """打印会话摘要"""
        summary = self.get_session_summary()
        
        if "error" in summary:
            print(f"❌ 获取摘要失败: {summary['error']}")
            return
        
        print("\n" + "="*60)
        print(f"📊 {self.project_name} Token使用摘要")
        print("="*60)
        print(f"🆔 Session ID: {summary['session_id']}")
        print(f"⏱️  持续时间: {summary['duration']}")
        print(f"🔢 总Token数: {summary['total_tokens']:,}")
        print(f"💰 总成本: ${summary['total_cost_usd']:.4f} (￥{summary['total_cost_cny']:.4f})")
        print(f"📞 API调用: {summary['api_calls']} 次")
        print(f"✅ 成功率: {summary['success_rate']:.1f}%")
        
        if summary['agents_used']:
            print(f"\n🤖 智能体使用:")
            for agent, count in summary['agents_used'].items():
                print(f"   • {agent}: {count} 次")
        
        if summary['operations']:
            print(f"\n⚙️  操作统计:")
            for operation, count in summary['operations'].items():
                if operation == "planning":
                    op_name = "规划"
                elif operation == "writing":
                    op_name = "写作"
                elif operation == "examining":
                    op_name = "审查"
                else:
                    op_name = operation
                print(f"   • {op_name}: {count} 次")
        
        print("="*60)

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if self.tracker:
            # 自动生成报告
            if self.enable_visualization:
                try:
                    self.generate_report()
                except Exception as e:
                    self.logger.error(f"自动生成报告失败: {e}")
            
            # 打印摘要
            self.print_summary()


# 便捷函数
def create_token_manager(session_id: Optional[str] = None,
                        project_name: str = "AutoPatent",
                        enable_visualization: bool = True) -> IntegratedTokenManager:
    """创建Token管理器的便捷函数"""
    return IntegratedTokenManager(
        session_id=session_id,
        project_name=project_name,
        enable_visualization=enable_visualization
    )


if __name__ == "__main__":
    # 演示用法
    print("🔧 集成Token管理器演示")
    
    with create_token_manager(session_id="demo_session") as manager:
        # 模拟智能体操作
        manager.track_agent_operation(
            agent_name="PlannerAgent",
            operation="planning",
            input_tokens=1500,
            output_tokens=800,
            model_name="deepseek-chat",
            response_time=2.5,
            success=True
        )
        
        manager.track_agent_operation(
            agent_name="WriterAgent",
            operation="writing",
            input_tokens=2000,
            output_tokens=1200,
            model_name="deepseek-chat",
            response_time=3.2,
            success=True
        )
        
        manager.track_agent_operation(
            agent_name="ExaminerAgent",
            operation="examining",
            input_tokens=1000,
            output_tokens=500,
            model_name="deepseek-chat",
            response_time=1.8,
            success=True
        )
        
        # 获取成本分解
        cost_breakdown = manager.get_cost_breakdown()
        print("\n💰 成本分解:")
        for operation, details in cost_breakdown.get("by_operation", {}).items():
            print(f"   {operation}: ￥{details['cost_cny']:.4f} ({details['tokens']} tokens)")
    
    print("\n✅ 演示完成！")
