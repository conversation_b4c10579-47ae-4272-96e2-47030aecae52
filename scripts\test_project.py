#!/usr/bin/env python3
"""
项目测试脚本 - 验证AutoPatent项目是否能正常运行
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有关键模块的导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试基础模块
        from agents.base_agent import BaseAgent, AgentStatus, AgentCapability
        print("✓ BaseAgent导入成功")
        
        from utils.token_tracker import TokenTracker, OperationType
        print("✓ TokenTracker导入成功")
        
        from utils.llm_client import LLMClient, ModelConfig, ModelProvider
        print("✓ LLMClient导入成功")
        
        from config import AutoPatentConfig
        print("✓ Config导入成功")
        
        # 测试智能体模块
        from agents.planner_agent import PlannerAgent
        print("✓ PlannerAgent导入成功")
        
        from agents.writer_agent import WriterAgent
        print("✓ WriterAgent导入成功")
        
        from agents.examiner_agent import ExaminerAgent
        print("✓ ExaminerAgent导入成功")
        
        # 测试协调器
        from coordinator.coordinator import AutoPatentCoordinator
        print("✓ AutoPatentCoordinator导入成功")
        
        # 测试数据库
        from database.patent_db import PatentDB
        print("✓ PatentDB导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")
    
    try:
        # 测试配置
        from config import AutoPatentConfig
        config = AutoPatentConfig()
        print("✓ 配置初始化成功")
        
        # 测试Token追踪器
        from utils.token_tracker import TokenTracker, OperationType
        tracker = TokenTracker()
        tracker.add_tokens(
            operation=OperationType.PLANNING,
            agent_name="test_agent",
            input_tokens=100,
            output_tokens=50,
            model_name="test_model",
            response_time=1.0,
            success=True
        )
        usage = tracker.get_usage()
        print(f"✓ Token追踪器工作正常，记录了 {usage['total_tokens']} 个token")
        
        # 测试数据库（不需要API密钥）
        from database.patent_db import PatentDB
        db = PatentDB(":memory:")  # 使用内存数据库
        print("✓ 数据库初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def test_environment():
    """测试环境配置"""
    print("\n🌍 测试环境配置...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    else:
        print(f"✓ Python版本: {sys.version}")
    
    # 检查API密钥
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if api_key:
        print("✓ DEEPSEEK_API_KEY已设置")
    else:
        print("⚠️  DEEPSEEK_API_KEY未设置（演示模式仍可运行）")
    
    # 检查必要的包
    required_packages = [
        'pydantic', 'python-dotenv', 'numpy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n需要安装的包: {', '.join(missing_packages)}")
        print("运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def test_demo_mode():
    """测试演示模式（不需要API密钥）"""
    print("\n🎭 测试演示模式...")
    
    try:
        # 创建一个简单的测试智能体
        from agents.base_agent import BaseAgent, AgentCapability
        
        class TestAgent(BaseAgent):
            def process(self, input_data):
                return {
                    'success': True,
                    'result': f"测试智能体处理了: {input_data.get('test_input', 'unknown')}"
                }
        
        # 测试智能体
        agent = TestAgent(
            agent_name="test_agent",
            capabilities=[AgentCapability.PLANNING]
        )
        
        result = agent.process({'test_input': 'hello world'})
        if result['success']:
            print("✓ 测试智能体工作正常")
        else:
            print("❌ 测试智能体失败")
            return False
        
        # 测试状态获取
        status = agent.get_status()
        print(f"✓ 智能体状态: {status['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示模式测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 AutoPatent项目测试开始")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("环境配置", test_environment),
        ("模块导入", test_imports),
        ("基本功能", test_basic_functionality),
        ("演示模式", test_demo_mode)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        if test_func():
            print(f"✅ {test_name}测试通过")
            passed += 1
        else:
            print(f"❌ {test_name}测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目可以正常运行")
        print("\n💡 下一步:")
        print("1. 设置DEEPSEEK_API_KEY环境变量")
        print("2. 运行: python run_autopatent.py --demo")
        print("3. 或运行: python run_autopatent.py --interactive")
    else:
        print("⚠️  部分测试失败，请检查上述错误信息")
        print("\n🔧 修复建议:")
        print("1. 安装缺失的依赖包")
        print("2. 检查Python版本是否>=3.8")
        print("3. 确保所有模块文件存在")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
