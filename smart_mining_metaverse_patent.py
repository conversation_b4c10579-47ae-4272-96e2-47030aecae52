#!/usr/bin/env python3
"""
智慧矿山元宇宙技术专利生成完整示例

本脚本演示了如何使用AutoPatent系统生成一个完整的专利申请，
包括多轮迭代优化和质量控制。
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from autopatent.agents.planner_agent import PlannerAgent, PlanningStrategy
from autopatent.agents.writer_agent import WriterAgent, WriterType, WritingStyle
from autopatent.agents.examiner_agent import ExaminerAgent
from autopatent.database.patent_db import PatentDB
from autopatent.utils.pg_tree_handler import PGTreeHandler, PGTreeNode, NodeStatus


class AdvancedPatentCoordinator:
    """高级专利协调器 - 实现完整的多智能体协作流程"""
    
    def __init__(self, 
                 max_iterations: int = 5,
                 quality_threshold: float = 8.0,
                 enable_iterative_improvement: bool = True):
        """
        初始化高级协调器
        
        Args:
            max_iterations: 最大迭代次数
            quality_threshold: 质量阈值
            enable_iterative_improvement: 是否启用迭代改进
        """
        self.max_iterations = max_iterations
        self.quality_threshold = quality_threshold
        self.enable_iterative_improvement = enable_iterative_improvement
        
        # 创建数据库
        self.db_path = "smart_mining_patents.db"
        self._setup_database()
        
        # 创建智能体团队
        self.planner = PlannerAgent(
            planning_strategy=PlanningStrategy.DETAILED,
            enable_adaptive_planning=True
        )
        
        self.writers = {
            'general': WriterAgent(WriterType.GENERAL, WritingStyle.FORMAL),
            'technical': WriterAgent(WriterType.TECHNICAL, WritingStyle.TECHNICAL),
            'legal': WriterAgent(WriterType.LEGAL, WritingStyle.LEGAL)
        }
        
        with PatentDB(self.db_path) as patent_db:
            self.examiner = ExaminerAgent(
                patent_db=patent_db,
                examination_standards={
                    'novelty_threshold': 0.7,
                    'inventiveness_threshold': 0.6,
                    'quality_threshold': self.quality_threshold,
                    'min_word_count': 200,
                    'max_similarity_allowed': 0.8
                },
                enable_rrag=True
            )
        
        print("🤖 高级专利协调器初始化完成")
        print(f"   📊 质量阈值: {quality_threshold}")
        print(f"   🔄 最大迭代: {max_iterations}")
        print(f"   ✨ 迭代改进: {'启用' if enable_iterative_improvement else '禁用'}")
    
    def _setup_database(self):
        """设置专利数据库，添加相关现有专利"""
        with PatentDB(self.db_path) as patent_db:
            # 添加一些相关的现有专利用于RRAG检索
            existing_patents = [
                {
                    'patent_id': 'CN202110123456A',
                    'title': '一种基于虚拟现实的矿山安全培训系统',
                    'abstract': '本发明提供一种基于虚拟现实技术的矿山安全培训系统，通过构建虚拟矿山环境，实现沉浸式安全培训。',
                    'technical_field': '矿山安全技术',
                    'quality_score': 7.8,
                    'filing_date': '2021-01-15T00:00:00'
                },
                {
                    'patent_id': 'CN202110234567A',
                    'title': '智能矿山数字孪生系统及其构建方法',
                    'abstract': '本发明涉及一种智能矿山数字孪生系统，通过物联网传感器和数字建模技术，构建矿山的数字化副本。',
                    'technical_field': '数字孪生技术',
                    'quality_score': 8.2,
                    'filing_date': '2021-02-20T00:00:00'
                },
                {
                    'patent_id': 'CN202110345678A',
                    'title': '基于区块链的矿山设备管理系统',
                    'abstract': '本发明提供一种基于区块链技术的矿山设备全生命周期管理系统，确保设备信息的可信性和可追溯性。',
                    'technical_field': '区块链技术',
                    'quality_score': 7.5,
                    'filing_date': '2021-03-10T00:00:00'
                }
            ]
            
            for patent in existing_patents:
                patent_db.add_patent(patent)
            
            print(f"📚 已添加 {len(existing_patents)} 个相关专利到数据库")
    
    def generate_patent(self, concept: str, options: dict = None) -> dict:
        """
        生成专利的完整流程
        
        Args:
            concept: 专利概念描述
            options: 生成选项
            
        Returns:
            生成结果字典
        """
        if not options:
            options = {}
        
        start_time = datetime.now()
        workflow_id = f"smart_mining_{int(time.time())}"
        
        print(f"\n🚀 开始生成专利 - 工作流ID: {workflow_id}")
        print(f"📝 概念: {concept[:100]}...")
        print("=" * 80)
        
        try:
            # 第一阶段：规划
            print("\n🎯 第一阶段：智能规划")
            print("-" * 40)
            
            planning_result = self.planner.process({
                'concept': concept,
                'options': options,
                'workflow_id': workflow_id
            })
            
            if not planning_result['success']:
                return {'success': False, 'error': f"规划失败: {planning_result['error']}"}
            
            pgtree_plan = planning_result['pgtree']
            print(f"✅ 规划完成 - 策略: {pgtree_plan.strategy.value}")
            print(f"📊 生成 {len(pgtree_plan.structure)} 个section")
            print(f"📏 预估总字数: {pgtree_plan.total_estimated_words}")
            
            # 转换为PGTreeHandler格式
            pgtree_handler = self._convert_to_pgtree_handler(pgtree_plan, concept)
            
            # 第二阶段：写作
            print(f"\n✍️  第二阶段：智能写作")
            print("-" * 40)
            
            writing_results = {}
            sections_to_write = ['abstract', 'background', 'summary', 'detailed_description', 'claims']
            
            for section in sections_to_write:
                if section in pgtree_handler.nodes:
                    print(f"  📝 写作 {section}...")
                    
                    # 选择合适的写作智能体
                    writer = self._select_writer(section)
                    
                    writing_result = writer.process({
                        'pgtree': pgtree_handler,
                        'section': section,
                        'concept': concept,
                        'technical_field': options.get('technical_field', '智慧矿山技术'),
                        'workflow_id': workflow_id
                    })
                    
                    if writing_result['success']:
                        # 更新PGTreeHandler
                        pgtree_handler = writing_result['pgtree']
                        writing_results[section] = writing_result
                        print(f"    ✅ {section} 完成 - 质量: {writing_result['quality_score']:.2f}/10")
                    else:
                        print(f"    ❌ {section} 失败: {writing_result['error']}")
            
            # 第三阶段：审查和迭代改进
            print(f"\n🔍 第三阶段：智能审查与迭代改进")
            print("-" * 40)
            
            final_result = self._iterative_examination_and_improvement(
                pgtree_handler, concept, options, workflow_id
            )
            
            # 生成最终报告
            total_time = (datetime.now() - start_time).total_seconds()
            
            return {
                'success': True,
                'workflow_id': workflow_id,
                'patent_content': self._extract_patent_content(pgtree_handler),
                'planning_result': planning_result,
                'writing_results': writing_results,
                'examination_result': final_result,
                'metadata': {
                    'total_processing_time': total_time,
                    'sections_generated': len(writing_results),
                    'final_quality_score': final_result.get('overall_score', 0),
                    'iterations_performed': final_result.get('iterations_performed', 0)
                }
            }
            
        except Exception as e:
            print(f"❌ 专利生成过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': str(e)}
    
    def _convert_to_pgtree_handler(self, pgtree_plan, concept: str) -> PGTreeHandler:
        """将规划结果转换为PGTreeHandler格式"""
        handler = PGTreeHandler()
        
        for section_id, section_plan in pgtree_plan.structure.items():
            node = PGTreeNode(
                node_id=section_id,
                section_name=section_plan.section_name,
                status=NodeStatus.PENDING,
                priority=section_plan.priority.value,
                content="",
                requirements=section_plan.requirements,
                writing_guide=section_plan.writing_guide,
                estimated_time=section_plan.estimated_time_minutes
            )
            handler.add_node(node)
        
        return handler
    
    def _select_writer(self, section: str) -> WriterAgent:
        """根据section选择合适的写作智能体"""
        if section in ['claims']:
            return self.writers['legal']
        elif section in ['detailed_description', 'background']:
            return self.writers['technical']
        else:
            return self.writers['general']
    
    def _iterative_examination_and_improvement(self, pgtree_handler, concept, options, workflow_id):
        """迭代审查和改进流程"""
        iteration = 0
        best_score = 0
        
        while iteration < self.max_iterations:
            iteration += 1
            print(f"\n🔄 第 {iteration} 轮审查...")
            
            # 执行审查
            with PatentDB(self.db_path) as patent_db:
                examiner = ExaminerAgent(patent_db=patent_db, enable_rrag=True)
                examination_result = examiner.process({
                    'pgtree': pgtree_handler,
                    'section': 'all',
                    'examination_type': 'substantive',
                    'workflow_id': workflow_id,
                    'iteration': iteration
                })
            
            if not examination_result['success']:
                print(f"    ❌ 审查失败: {examination_result['error']}")
                break
            
            current_score = examination_result['overall_score']
            issues_count = len(examination_result['issues'])
            
            print(f"    📊 当前评分: {current_score:.2f}/10")
            print(f"    ⚠️  发现问题: {issues_count} 个")
            
            # 检查是否达到质量阈值
            if current_score >= self.quality_threshold:
                print(f"    ✅ 质量达标！(>= {self.quality_threshold})")
                examination_result['iterations_performed'] = iteration
                return examination_result
            
            # 如果不启用迭代改进，直接返回
            if not self.enable_iterative_improvement:
                examination_result['iterations_performed'] = iteration
                return examination_result
            
            # 如果评分没有提升，停止迭代
            if current_score <= best_score and iteration > 1:
                print(f"    🛑 评分未提升，停止迭代")
                break
            
            best_score = max(best_score, current_score)
            
            # 执行改进
            if issues_count > 0 and iteration < self.max_iterations:
                print(f"    🔧 执行改进...")
                self._improve_content(pgtree_handler, examination_result['issues'], workflow_id)
        
        examination_result['iterations_performed'] = iteration
        return examination_result
    
    def _improve_content(self, pgtree_handler, issues, workflow_id):
        """根据审查问题改进内容"""
        # 按section分组问题
        issues_by_section = {}
        for issue in issues:
            section = issue.get('section', 'unknown')
            if section not in issues_by_section:
                issues_by_section[section] = []
            issues_by_section[section].append(issue)
        
        # 改进每个有问题的section
        for section, section_issues in issues_by_section.items():
            if section in pgtree_handler.nodes and section != 'unknown':
                print(f"      🔧 改进 {section}...")
                
                # 生成改进反馈
                feedback = []
                for issue in section_issues:
                    if issue.get('suggestion'):
                        feedback.append(issue['suggestion'])
                
                if feedback:
                    writer = self._select_writer(section)
                    current_content = pgtree_handler.nodes[section].content
                    
                    revised_content = writer.revise_content(
                        current_content=current_content,
                        feedback=feedback,
                        section=section
                    )
                    
                    if revised_content and revised_content != current_content:
                        # 更新内容
                        pgtree_handler.update_node_content(
                            node_id=section,
                            content=revised_content,
                            status=NodeStatus.COMPLETED
                        )
                        print(f"        ✅ {section} 改进完成")
                    else:
                        print(f"        ⚠️  {section} 改进效果有限")
    
    def _extract_patent_content(self, pgtree_handler) -> dict:
        """提取专利内容"""
        content = {}
        for node_id, node in pgtree_handler.nodes.items():
            if node.content:
                content[node_id] = node.content
        return content


def main():
    """主函数 - 智慧矿山元宇宙专利生成"""
    print("🏔️  智慧矿山元宇宙技术专利生成系统")
    print("=" * 80)
    
    # 定义专利概念
    concept = """
    一种基于元宇宙技术的智慧矿山综合管理系统，该系统融合了虚拟现实(VR)、增强现实(AR)、
    数字孪生、人工智能、物联网、区块链等前沿技术，构建矿山作业的沉浸式虚拟环境。
    
    系统的核心创新包括：
    
    1. 矿山元宇宙空间构建技术
       - 基于激光雷达和摄影测量的高精度三维重建
       - 实时动态环境更新和渲染优化
       - 多尺度空间建模（从设备级到矿区级）
    
    2. 沉浸式人机交互技术
       - 多模态交互界面（手势、语音、眼动、脑机接口）
       - 触觉反馈和力觉模拟
       - 虚拟协作和远程操控
    
    3. 智能决策支持系统
       - 基于数字孪生的实时状态监测
       - AI驱动的预测性维护和风险评估
       - 智能调度和资源优化算法
    
    4. 区块链安全保障机制
       - 分布式身份认证和权限管理
       - 操作记录的不可篡改存储
       - 智能合约驱动的自动化流程
    
    5. 跨平台兼容性技术
       - 支持PC、移动设备、VR/AR头显等多终端
       - 云边协同计算架构
       - 5G/6G网络优化传输
    
    应用场景包括：矿山安全培训、远程设备操控、应急演练、生产调度、
    设备维护、地质勘探、环境监测等。该系统能够显著提升矿山作业的
    安全性、效率和智能化水平。
    """
    
    # 配置生成选项
    options = {
        'technical_field': '智慧矿山与元宇宙技术',
        'patent_type': 'invention',
        'target_quality': 8.5,
        'enable_detailed_description': True,
        'claim_count': {'independent': 3, 'dependent': 12}
    }
    
    # 创建高级协调器
    coordinator = AdvancedPatentCoordinator(
        max_iterations=3,
        quality_threshold=8.0,
        enable_iterative_improvement=True
    )
    
    # 生成专利
    result = coordinator.generate_patent(concept, options)
    
    # 处理结果
    if result['success']:
        print(f"\n🎉 专利生成成功！")
        print("=" * 80)
        
        metadata = result['metadata']
        print(f"📊 最终质量评分: {metadata['final_quality_score']:.2f}/10")
        print(f"⏱️  总处理时间: {metadata['total_processing_time']:.2f}秒")
        print(f"📝 生成section数: {metadata['sections_generated']}")
        print(f"🔄 执行迭代次数: {metadata['iterations_performed']}")
        
        # 保存完整结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"smart_mining_metaverse_patent_{timestamp}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 完整结果已保存到: {output_file}")
        
        # 显示专利内容预览
        print(f"\n📄 专利内容预览:")
        print("-" * 80)
        
        patent_content = result['patent_content']
        for section, content in patent_content.items():
            print(f"\n【{section.upper()}】")
            preview = content[:300] + "..." if len(content) > 300 else content
            print(preview)
        
        # 显示审查结果
        examination = result['examination_result']
        if examination.get('rrag_result'):
            rrag = examination['rrag_result']
            print(f"\n🔬 RRAG分析结果:")
            print(f"  新颖性评分: {rrag['novelty_analysis']['novelty_score']:.2f}/10")
            print(f"  相似专利数: {len(rrag['retrieved_patents'])}")
            print(f"  风险评估: {rrag['risk_assessment']}")
        
    else:
        print(f"\n❌ 专利生成失败: {result['error']}")


if __name__ == "__main__":
    main()
