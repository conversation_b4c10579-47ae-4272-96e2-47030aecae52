#!/usr/bin/env python3
"""
项目结构整理脚本 - 按功能归类文件
"""

import os
import shutil
from pathlib import Path
import json

def organize_project_structure():
    """整理项目结构"""
    print("🗂️  开始整理项目结构...")
    
    # 项目根目录
    root_dir = Path(__file__).parent.parent
    
    # 创建目录结构
    directories = {
        'scripts': '脚本文件',
        'docs': '文档文件', 
        'examples': '示例文件',
        'tests': '测试文件',
        'output': '输出文件',
        'config': '配置文件',
        'logs': '日志文件'
    }
    
    for dir_name, description in directories.items():
        dir_path = root_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        print(f"✅ 创建目录: {dir_name} ({description})")
    
    # 文件移动规则
    move_rules = [
        # 脚本文件
        {
            'files': [
                'fix_imports.py',
                'install_dependencies.py', 
                'test_project.py',
                'run_autopatent_uv.py',
                'simple_demo.py'
            ],
            'target': 'scripts',
            'description': '脚本文件'
        },
        
        # 文档文件
        {
            'files': [
                'README_FIXES.md',
                'README_UV.md',
                'LICENSE'
            ],
            'target': 'docs',
            'description': '文档文件'
        },
        
        # 示例文件
        {
            'files': [
                'example_usage.py'
            ],
            'target': 'examples',
            'description': '示例文件'
        },
        
        # 配置文件
        {
            'files': [
                'requirements.txt',
                'pyproject.toml'
            ],
            'target': 'config',
            'description': '配置文件'
        },
        
        # 输出文件（JSON结果）
        {
            'pattern': '*.json',
            'target': 'output/results',
            'description': '生成的结果文件'
        }
    ]
    
    # 执行文件移动
    for rule in move_rules:
        target_dir = root_dir / rule['target']
        target_dir.mkdir(parents=True, exist_ok=True)
        
        if 'files' in rule:
            # 移动指定文件
            for file_name in rule['files']:
                source_file = root_dir / file_name
                if source_file.exists():
                    target_file = target_dir / file_name
                    if not target_file.exists():
                        shutil.move(str(source_file), str(target_file))
                        print(f"📁 移动文件: {file_name} -> {rule['target']}")
        
        elif 'pattern' in rule:
            # 移动匹配模式的文件
            import glob
            pattern_files = glob.glob(str(root_dir / rule['pattern']))
            for file_path in pattern_files:
                file_name = os.path.basename(file_path)
                target_file = target_dir / file_name
                if not target_file.exists():
                    shutil.move(file_path, str(target_file))
                    print(f"📁 移动文件: {file_name} -> {rule['target']}")
    
    # 创建新的主启动脚本
    create_main_launcher(root_dir)
    
    # 创建项目结构说明文件
    create_structure_readme(root_dir)
    
    print("\n🎉 项目结构整理完成！")

def create_main_launcher(root_dir: Path):
    """创建主启动脚本"""
    launcher_content = '''#!/usr/bin/env python3
"""
AutoPatent主启动器 - 统一入口
"""

import sys
import argparse
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AutoPatent - 多智能体专利生成系统')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 运行命令
    run_parser = subparsers.add_parser('run', help='运行专利生成')
    run_parser.add_argument('--demo', action='store_true', help='演示模式')
    run_parser.add_argument('--interactive', '-i', action='store_true', help='交互模式')
    run_parser.add_argument('--concept', '-c', type=str, help='专利概念')
    run_parser.add_argument('--field', '-f', type=str, default='通用技术', help='技术领域')
    run_parser.add_argument('--output', '-o', type=str, help='输出文件')
    
    # 测试命令
    test_parser = subparsers.add_parser('test', help='运行测试')
    test_parser.add_argument('--module', '-m', type=str, help='测试特定模块')
    
    # 可视化命令
    viz_parser = subparsers.add_parser('visualize', help='生成可视化报告')
    viz_parser.add_argument('--input', '-i', type=str, help='输入数据文件')
    viz_parser.add_argument('--output', '-o', type=str, default='reports', help='输出目录')
    
    # 工具命令
    tools_parser = subparsers.add_parser('tools', help='工具命令')
    tools_parser.add_argument('--organize', action='store_true', help='整理项目结构')
    tools_parser.add_argument('--install-deps', action='store_true', help='安装依赖')
    
    args = parser.parse_args()
    
    if args.command == 'run':
        from scripts.run_autopatent_uv import main as run_main
        # 重新构造参数
        sys.argv = ['run_autopatent_uv.py']
        if args.demo:
            sys.argv.append('--demo')
        elif args.interactive:
            sys.argv.append('--interactive')
        elif args.concept:
            sys.argv.extend(['-c', args.concept, '-f', args.field])
            if args.output:
                sys.argv.extend(['-o', args.output])
        run_main()
        
    elif args.command == 'test':
        from scripts.test_project import main as test_main
        test_main()
        
    elif args.command == 'visualize':
        print("🎨 启动可视化功能...")
        try:
            from autopatent.utils.enhanced_token_visualizer import EnhancedTokenVisualizer
            
            if args.input:
                visualizer = EnhancedTokenVisualizer.from_export_file(args.input)
            else:
                visualizer = EnhancedTokenVisualizer()
                
            report_path = visualizer.create_enhanced_dashboard(args.output)
            print(f"✅ 可视化报告已生成: {report_path}")
        except Exception as e:
            print(f"❌ 生成可视化报告失败: {e}")
            
    elif args.command == 'tools':
        if args.organize:
            from scripts.organize_project import organize_project_structure
            organize_project_structure()
        elif args.install_deps:
            from scripts.install_dependencies import main as install_main
            install_main()
        else:
            tools_parser.print_help()
            
    else:
        parser.print_help()
        print("\\n💡 常用命令:")
        print("  python autopatent.py run --demo                    # 运行演示")
        print("  python autopatent.py run --interactive             # 交互模式")
        print("  python autopatent.py run -c '您的概念' -f '技术领域'  # 直接生成")
        print("  python autopatent.py test                          # 运行测试")
        print("  python autopatent.py visualize -i data.json        # 生成可视化")
        print("  python autopatent.py tools --organize              # 整理项目")

if __name__ == "__main__":
    main()
'''
    
    launcher_path = root_dir / 'autopatent.py'
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print(f"✅ 创建主启动器: autopatent.py")

def create_structure_readme(root_dir: Path):
    """创建项目结构说明"""
    readme_content = '''# AutoPatent项目结构说明

## 📁 目录结构

```
AutoPatent/
├── autopatent.py              # 🚀 主启动器（统一入口）
├── README.md                  # 📖 主要说明文档
├── 
├── autopatent/                # 📦 核心包
│   ├── __init__.py
│   ├── cli.py                 # 命令行界面
│   ├── agents/                # 🤖 智能体模块
│   ├── coordinator/           # 🎯 协调器模块
│   ├── database/              # 💾 数据库模块
│   ├── utils/                 # 🛠️ 工具模块
│   └── examples/              # 📝 示例模块
│
├── scripts/                   # 📜 脚本文件
│   ├── run_autopatent_uv.py   # uv环境运行脚本
│   ├── test_project.py        # 项目测试脚本
│   ├── install_dependencies.py # 依赖安装脚本
│   ├── fix_imports.py         # 导入修复脚本
│   └── organize_project.py    # 项目整理脚本
│
├── docs/                      # 📚 文档文件
│   ├── README_FIXES.md        # 修复说明
│   ├── README_UV.md           # uv环境说明
│   └── LICENSE                # 许可证
│
├── config/                    # ⚙️ 配置文件
│   ├── requirements.txt       # Python依赖
│   ├── pyproject.toml         # 项目配置
│   └── .env.example           # 环境变量示例
│
├── examples/                  # 📋 示例文件
│   └── example_usage.py       # 使用示例
│
├── tests/                     # 🧪 测试文件
│   └── (测试文件)
│
├── output/                    # 📤 输出文件
│   ├── results/               # 生成结果
│   ├── reports/               # 可视化报告
│   └── logs/                  # 日志文件
│
└── logs/                      # 📋 日志文件
```

## 🚀 快速开始

### 使用主启动器
```bash
# 运行演示
python autopatent.py run --demo

# 交互模式
python autopatent.py run --interactive

# 直接生成专利
python autopatent.py run -c "您的专利概念" -f "技术领域"

# 运行测试
python autopatent.py test

# 生成可视化报告
python autopatent.py visualize -i output/data.json

# 整理项目结构
python autopatent.py tools --organize
```

### 直接使用脚本
```bash
# uv环境运行
python scripts/run_autopatent_uv.py --demo

# 项目测试
python scripts/test_project.py

# 安装依赖
python scripts/install_dependencies.py
```

## 📦 核心模块说明

### autopatent/
- **agents/**: 智能体实现（规划、写作、审查）
- **coordinator/**: 协调器和工作流管理
- **database/**: 数据库操作和存储
- **utils/**: 工具类（Token追踪、可视化、LLM客户端）
- **examples/**: 示例和演示代码

### scripts/
- **run_autopatent_uv.py**: uv环境专用启动脚本
- **test_project.py**: 项目功能测试
- **install_dependencies.py**: 自动安装依赖
- **organize_project.py**: 项目结构整理

### 特色功能

#### 🎨 增强版Token可视化
- 中英混合UI界面
- 支持人民币计费显示
- 专业术语保持英文
- 详细的使用统计和趋势分析

#### 🤖 多智能体协作
- 规划智能体：分析需求，制定专利结构
- 写作智能体：生成各部分内容
- 审查智能体：质量检查和优化建议

#### 📊 完整的监控体系
- Token使用追踪
- 成本分析（USD/CNY双币种）
- 性能监控
- 可视化报告

## 🔧 开发指南

### 添加新功能
1. 在相应的模块目录下添加代码
2. 更新`__init__.py`文件
3. 添加测试用例
4. 更新文档

### 项目维护
```bash
# 整理项目结构
python autopatent.py tools --organize

# 更新依赖
python autopatent.py tools --install-deps

# 运行完整测试
python autopatent.py test
```

## 📝 注意事项

1. **环境配置**: 确保已正确配置Python环境和API密钥
2. **依赖管理**: 使用uv或pip管理依赖包
3. **文件组织**: 遵循既定的目录结构
4. **代码规范**: 保持中英文注释的一致性

---

**AutoPatent** - 让专利申请更智能、更高效！
'''
    
    structure_readme_path = root_dir / 'docs' / 'PROJECT_STRUCTURE.md'
    structure_readme_path.parent.mkdir(exist_ok=True)
    
    with open(structure_readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 创建结构说明: docs/PROJECT_STRUCTURE.md")

if __name__ == "__main__":
    organize_project_structure()
