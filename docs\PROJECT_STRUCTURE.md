# AutoPatent项目结构说明

## 📁 目录结构

```
AutoPatent/
├── autopatent.py              # 🚀 主启动器（统一入口）
├── README.md                  # 📖 主要说明文档
├── 
├── autopatent/                # 📦 核心包
│   ├── __init__.py
│   ├── cli.py                 # 命令行界面
│   ├── agents/                # 🤖 智能体模块
│   ├── coordinator/           # 🎯 协调器模块
│   ├── database/              # 💾 数据库模块
│   ├── utils/                 # 🛠️ 工具模块
│   └── examples/              # 📝 示例模块
│
├── scripts/                   # 📜 脚本文件
│   ├── run_autopatent_uv.py   # uv环境运行脚本
│   ├── test_project.py        # 项目测试脚本
│   ├── install_dependencies.py # 依赖安装脚本
│   ├── fix_imports.py         # 导入修复脚本
│   └── organize_project.py    # 项目整理脚本
│
├── docs/                      # 📚 文档文件
│   ├── README_FIXES.md        # 修复说明
│   ├── README_UV.md           # uv环境说明
│   └── LICENSE                # 许可证
│
├── config/                    # ⚙️ 配置文件
│   ├── requirements.txt       # Python依赖
│   ├── pyproject.toml         # 项目配置
│   └── .env.example           # 环境变量示例
│
├── examples/                  # 📋 示例文件
│   └── example_usage.py       # 使用示例
│
├── tests/                     # 🧪 测试文件
│   └── (测试文件)
│
├── output/                    # 📤 输出文件
│   ├── results/               # 生成结果
│   ├── reports/               # 可视化报告
│   └── logs/                  # 日志文件
│
└── logs/                      # 📋 日志文件
```

## 🚀 快速开始

### 使用主启动器
```bash
# 运行演示
python autopatent.py run --demo

# 交互模式
python autopatent.py run --interactive

# 直接生成专利
python autopatent.py run -c "您的专利概念" -f "技术领域"

# 运行测试
python autopatent.py test

# 生成可视化报告
python autopatent.py visualize -i output/data.json

# 整理项目结构
python autopatent.py tools --organize
```

### 直接使用脚本
```bash
# uv环境运行
python scripts/run_autopatent_uv.py --demo

# 项目测试
python scripts/test_project.py

# 安装依赖
python scripts/install_dependencies.py
```

## 📦 核心模块说明

### autopatent/
- **agents/**: 智能体实现（规划、写作、审查）
- **coordinator/**: 协调器和工作流管理
- **database/**: 数据库操作和存储
- **utils/**: 工具类（Token追踪、可视化、LLM客户端）
- **examples/**: 示例和演示代码

### scripts/
- **run_autopatent_uv.py**: uv环境专用启动脚本
- **test_project.py**: 项目功能测试
- **install_dependencies.py**: 自动安装依赖
- **organize_project.py**: 项目结构整理

### 特色功能

#### 🎨 增强版Token可视化
- 中英混合UI界面
- 支持人民币计费显示
- 专业术语保持英文
- 详细的使用统计和趋势分析

#### 🤖 多智能体协作
- 规划智能体：分析需求，制定专利结构
- 写作智能体：生成各部分内容
- 审查智能体：质量检查和优化建议

#### 📊 完整的监控体系
- Token使用追踪
- 成本分析（USD/CNY双币种）
- 性能监控
- 可视化报告

## 🔧 开发指南

### 添加新功能
1. 在相应的模块目录下添加代码
2. 更新`__init__.py`文件
3. 添加测试用例
4. 更新文档

### 项目维护
```bash
# 整理项目结构
python autopatent.py tools --organize

# 更新依赖
python autopatent.py tools --install-deps

# 运行完整测试
python autopatent.py test
```

## 📝 注意事项

1. **环境配置**: 确保已正确配置Python环境和API密钥
2. **依赖管理**: 使用uv或pip管理依赖包
3. **文件组织**: 遵循既定的目录结构
4. **代码规范**: 保持中英文注释的一致性

---

**AutoPatent** - 让专利申请更智能、更高效！
