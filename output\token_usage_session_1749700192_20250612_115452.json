{
  "导出信息": {
    "导出时间": "2025-06-12T11:54:52.389507",
    "session_id": "session_1749700192",
    "记录数量": 2,
    "格式版本": "1.0"
  },
  "session统计": {
    "session_id": "session_1749700192",
    "start_time": "2025-06-12 11:49:52.383758",
    "total_input_tokens": 745,
    "total_output_tokens": 310,
    "total_reasoning_tokens": 0,
    "total_cost": 0.000191,
    "api_calls": 1,
    "successful_calls": 1,
    "failed_calls": 0,
    "operations": {
      "content_generation": 1
    },
    "models_used": {
      "deepseek-chat": 1
    },
    "agents_used": {
      "LLMClient": 1
    },
    "duration_seconds": 300.005749,
    "duration_formatted": "0:05:00",
    "total_tokens": 1055,
    "average_response_time": 21.209290266036987,
    "success_rate": 100.0
  },
  "详细分析": {
    "总记录数": 2,
    "时间范围": {
      "开始时间": "2025-06-12T11:50:15.107821",
      "结束时间": "2025-06-12T11:50:15.107821"
    },
    "token统计": {
      "input": 745,
      "output": 310
    },
    "成本统计": {
      "input": 0.000104,
      "output": 8.7e-05
    },
    "总成本": 0.000191,
    "操作统计": {
      "content_generation": {
        "次数": 2,
        "tokens": 1055,
        "成本": 0.000191
      }
    },
    "智能体统计": {
      "LLMClient": {
        "次数": 2,
        "tokens": 1055,
        "成本": 0.000191
      }
    },
    "模型统计": {
      "deepseek-chat": {
        "次数": 2,
        "tokens": 1055,
        "成本": 0.000191
      }
    },
    "性能统计": {
      "成功率": 100.0,
      "平均响应时间": 21.209290266036987,
      "最快响应": 21.209290266036987,
      "最慢响应": 21.209290266036987,
      "失败次数": 0
    },
    "时间趋势": {
      "2025-06-12 11:00": {
        "tokens": 1055,
        "成本": 0.00019099999999999998,
        "调用次数": 2
      }
    }
  },
  "成本估算": {
    "input_cost": 0.000104,
    "output_cost": 8.7e-05,
    "reasoning_cost": 0.0,
    "total_cost": 0.000191,
    "input_rate": 0.00014,
    "output_rate": 0.00028,
    "currency": "USD"
  },
  "原始记录": [
    {
      "timestamp": "2025-06-12T11:50:15.107821",
      "operation": "content_generation",
      "agent_name": "LLMClient",
      "token_type": "input",
      "count": 745,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000104,
      "response_time": 21.209290266036987,
      "session_id": "session_1749700192",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:50:15.107821",
      "operation": "content_generation",
      "agent_name": "LLMClient",
      "token_type": "output",
      "count": 310,
      "model_name": "deepseek-chat",
      "cost_estimate": 8.7e-05,
      "response_time": 21.209290266036987,
      "session_id": "session_1749700192",
      "workflow_id": "",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 1.0,
      "success": true,
      "error_message": ""
    }
  ]
} "agent_name": "PlannerAgent",
      "token_type": "input",
      "count": 834,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000117,
      "response_time": 22.730065,
      "session_id": "session_1749700192",
      "workflow_id": "smart_mining_metaverse_fixed_1749700192",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 6.5,
      "success": true,
      "error_message": ""
    },
    {
      "timestamp": "2025-06-12T11:50:15.108817",
      "operation": "planning",
      "agent_name": "PlannerAgent",
      "token_type": "output",
      "count": 2688,
      "model_name": "deepseek-chat",
      "cost_estimate": 0.000753,
      "response_time": 22.730065,
      "session_id": "session_1749700192",
      "workflow_id": "smart_mining_metaverse_fixed_1749700192",
      "node_id": "",
      "input_length": 0,
      "output_length": 0,
      "complexity_score": 6.5,
      "success": true,
      "error_message": ""
    }
  ]
}