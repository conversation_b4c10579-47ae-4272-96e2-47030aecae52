"""
AutoPatent框架使用示例
"""

import logging
import os
from coordinator.coordinator import AutoPatentCoordinator
from database.patent_db import PatentDB

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("autopatent.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)


def main():
    """AutoPatent使用示例"""

    print("=" * 60)
    print("AutoPatent - 多智能体专利自动生成系统")
    print("=" * 60)

    # 检查环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("错误：请设置DEEPSEEK_API_KEY环境变量")
        return

    try:
        # 初始化协调器
        print("\n正在初始化AutoPatent协调器...")
        coordinator = AutoPatentCoordinator(
            db_path="example_patents.db", max_iterations=2, quality_threshold=6.5
        )
        print("✓ 协调器初始化完成")

        # 示例专利概念
        concept = """
        一种基于机器学习的智能家居能耗管理系统，该系统通过预测用户用电模式并自动控制智能设备来优化能耗。

        系统的核心创新包括：
        1. 实时用电需求预测算法，基于历史数据和用户行为模式
        2. 自适应调度算法，能够根据电价波动和用户偏好动态调整设备运行
        3. 多协议集成技术，无缝连接各种智能家居设备（WiFi, Zigbee, Z-Wave等）
        4. 与太阳能板、储能电池和电网的智能集成
        5. 用户友好的移动应用界面，提供实时监控和手动控制功能

        该系统能够在保持用户舒适度的前提下，最大化降低家庭能耗成本，预计节能效果可达20-30%。
        """

        # 生成选项
        options = {
            "technical_field": "智能家居技术",
            "target_length": "comprehensive",  # brief, standard, comprehensive
            "focus_areas": ["机器学习", "能耗优化", "物联网集成"],
            "claim_count": {"independent": 3, "dependent": 12},
            "language": "chinese",
            "patent_type": "invention",  # invention, utility_model, design
        }

        print(f"\n发明概念：{concept[:150]}...")
        print(f"技术领域：{options['technical_field']}")
        print(
            f"权利要求数量：独立 {options['claim_count']['independent']} 项，从属 {options['claim_count']['dependent']} 项"
        )

        # 开始生成专利
        print("\n开始专利生成过程...")
        print("-" * 40)

        result = coordinator.generate_patent(concept, options)

        # 处理结果
        if result["success"]:
            print("\n✓ 专利生成成功！")
            print(f"✓ 工作流状态：{result['workflow_state']}")
            print(f"✓ 迭代次数：{result['statistics']['iterations']}")
            print(f"✓ 完成时间：{result['statistics']['completion_time']}")

            # 显示生成的专利内容概要
            patent_content = result["patent_content"]
            print("\n生成的专利内容概要：")
            print("-" * 40)

            for section, content in patent_content.items():
                print(f"\n【{section.upper()}】")
                # 显示前200个字符
                preview = content[:200] + "..." if len(content) > 200 else content
                print(preview)

            # 显示令牌使用统计
            token_usage = result["token_usage"]
            print(f"\n令牌使用统计：")
            print(f"- 总输入令牌：{token_usage['total']['input_tokens']}")
            print(f"- 总输出令牌：{token_usage['total']['output_tokens']}")
            print(f"- 总令牌数：{token_usage['total']['total_tokens']}")

            # 保存专利到数据库
            patent_id = f"CN_{int(time.time())}"
            if coordinator.save_patent(patent_id):
                print(f"✓ 专利已保存到数据库，ID: {patent_id}")
            else:
                print("⚠ 专利保存失败")

            # 导出完整结果
            output_file = f"patent_result_{int(time.time())}.json"
            with open(output_file, "w", encoding="utf-8") as f:
                import json

                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            print(f"✓ 完整结果已导出到：{output_file}")

        else:
            print("\n✗ 专利生成失败")
            print(f"错误信息：{result['error']}")
            if "details" in result:
                print(f"详细信息：{result['details']}")

        # 显示数据库统计
        print("\n数据库统计信息：")
        db_stats = coordinator.patent_db.get_statistics()
        print(f"- 专利总数：{db_stats.get('total_patents', 0)}")
        print(f"- 技术领域数：{db_stats.get('technical_fields', 0)}")

    except Exception as e:
        print(f"\n系统错误：{e}")
        logging.error(f"系统错误：{e}", exc_info=True)


def demo_database_operations():
    """演示数据库操作"""
    print("\n" + "=" * 40)
    print("数据库操作演示")
    print("=" * 40)

    # 初始化数据库
    db = PatentDB("demo_patents.db")

    # 添加示例专利
    sample_patent = {
        "patent_id": "CN123456789A",
        "title": "一种智能家居能耗管理系统",
        "abstract": "本发明提供了一种基于机器学习的智能家居能耗管理系统...",
        "claims": "1. 一种智能家居能耗管理系统，其特征在于...",
        "description": "本发明涉及智能家居技术领域，特别是涉及一种能耗管理系统...",
        "technical_field": "智能家居技术",
        "inventors": "张三;李四",
        "assignee": "某某科技有限公司",
    }

    if db.add_patent(sample_patent):
        print("✓ 示例专利添加成功")

    # 搜索专利
    results = db.search_patents("智能家居", limit=5)
    print(f"✓ 搜索到 {len(results)} 个相关专利")

    # 相似专利搜索
    similar = db.search_similar_patents("机器学习能耗优化", limit=3)
    print(f"✓ 找到 {len(similar)} 个相似专利")


if __name__ == "__main__":
    import time

    # 运行主示例
    main()

    # 运行数据库操作演示
    demo_database_operations()

    print("\n" + "=" * 60)
    print("AutoPatent演示完成")
    print("=" * 60)
